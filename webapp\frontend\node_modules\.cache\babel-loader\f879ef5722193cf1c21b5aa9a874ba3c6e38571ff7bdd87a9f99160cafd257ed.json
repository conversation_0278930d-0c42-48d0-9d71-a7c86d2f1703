{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\nconst comandeService = {\n  // Ottiene la lista delle comande di un cantiere\n  getComande: async (cantiereId, params = {}) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}`, {\n        params\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Get comande error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea una nuova comanda\n  createComanda: async comandaData => {\n    try {\n      const response = await axiosInstance.post('/comande/', comandaData);\n      return response.data;\n    } catch (error) {\n      console.error('Create comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i dettagli di una comanda\n  getDettagliComanda: async codiceComanda => {\n    try {\n      const response = await axiosInstance.get(`/comande/${codiceComanda}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get dettagli comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Assegna cavi a una comanda\n  assegnaCavi: async (codiceComanda, listaIdCavi) => {\n    try {\n      const response = await axiosInstance.post(`/comande/${codiceComanda}/assegna-cavi`, {\n        lista_id_cavi: listaIdCavi\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Assegna cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna i dati di posa\n  aggiornaDatiPosa: async (codiceComanda, datiPosa) => {\n    try {\n      const response = await axiosInstance.put(`/comande/${codiceComanda}/dati-posa`, {\n        dati_posa: datiPosa\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Aggiorna dati posa error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna i dati di collegamento\n  aggiornaDatiCollegamento: async (codiceComanda, datiCollegamento) => {\n    try {\n      const response = await axiosInstance.put(`/comande/${codiceComanda}/dati-collegamento`, {\n        dati_collegamento: datiCollegamento\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Aggiorna dati collegamento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Cambia lo stato di una comanda\n  cambiaStato: async (codiceComanda, nuovoStato) => {\n    try {\n      const response = await axiosInstance.put(`/comande/${codiceComanda}/stato`, {\n        nuovo_stato: nuovoStato\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Cambia stato error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene le statistiche delle comande per un cantiere\n  getStatisticheComande: async cantiereId => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}/statistiche`);\n      return response.data;\n    } catch (error) {\n      console.error('Get statistiche comande error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i cavi assegnati a una comanda\n  getCaviComanda: async codiceComanda => {\n    try {\n      const response = await axiosInstance.get(`/comande/${codiceComanda}/cavi`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna i dati di posa per una comanda\n  aggiornaDatiPosa: async (codiceComanda, datiPosa) => {\n    try {\n      const response = await axiosInstance.post(`/comande/${codiceComanda}/dati-posa`, datiPosa);\n      return response.data;\n    } catch (error) {\n      console.error('Aggiorna dati posa error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna i dati di collegamento per una comanda\n  aggiornaDatiCollegamento: async (codiceComanda, datiCollegamento) => {\n    try {\n      const response = await axiosInstance.post(`/comande/${codiceComanda}/dati-collegamento`, datiCollegamento);\n      return response.data;\n    } catch (error) {\n      console.error('Aggiorna dati collegamento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna i dati di certificazione per una comanda\n  aggiornaDatiCertificazione: async (codiceComanda, datiCertificazione) => {\n    try {\n      const response = await axiosInstance.post(`/comande/${codiceComanda}/dati-certificazione`, datiCertificazione);\n      return response.data;\n    } catch (error) {\n      console.error('Aggiorna dati certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene le comande per un responsabile specifico\n  getComandeByResponsabile: async (cantiereId, nomeResponsabile) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}`, {\n        params: {\n          responsabile: nomeResponsabile\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Get comande by responsabile error:', error);\n      // Non lanciare errore, ritorna array vuoto\n      return [];\n    }\n  },\n  // Aggiorna una comanda esistente (solo campi modificabili)\n  updateComanda: async (codiceComanda, comandaData) => {\n    try {\n      console.log('🔄 Aggiornamento comanda:', {\n        codiceComanda,\n        comandaData\n      });\n      const response = await axiosInstance.put(`/comande/${codiceComanda}`, comandaData);\n      console.log('✅ Comanda aggiornata:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Errore aggiornamento comanda:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina una comanda\n  deleteComanda: async codiceComanda => {\n    try {\n      const response = await axiosInstance.delete(`/comande/${codiceComanda}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Assegna una comanda a un cavo\n  assignComandaToCavo: async (cantiereId, idComanda, idCavo) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/comande/${cantiereIdNum}/${idComanda}/assign`, {\n        id_cavo: idCavo\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Assign comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Genera PDF di una comanda\n  printComanda: async (cantiereId, idComanda) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/comande/${cantiereIdNum}/${idComanda}/pdf`);\n      return response.data;\n    } catch (error) {\n      console.error('Print comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i cavi disponibili per un tipo di comanda\n  getCaviDisponibili: async (cantiereId, tipoComanda) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}/cavi-disponibili`, {\n        params: {\n          tipo_comanda: tipoComanda\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi disponibili error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea una comanda con cavi pre-selezionati\n  createComandaConCavi: async (cantiereId, comandaData, listaIdCavi) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Prepara il payload completo con i dati della comanda e la lista dei cavi\n      const payload = {\n        ...comandaData,\n        lista_id_cavi: listaIdCavi\n      };\n      console.log('🚀 Creazione comanda con cavi:', {\n        cantiereId: cantiereIdNum,\n        tipoComanda: comandaData.tipo_comanda,\n        responsabile: comandaData.responsabile,\n        numeroCavi: listaIdCavi.length,\n        cavi: listaIdCavi\n      });\n      const response = await axiosInstance.post(`/comande/cantiere/${cantiereIdNum}/crea-con-cavi?${listaIdCavi.map(id => `lista_id_cavi=${encodeURIComponent(id)}`).join('&')}`, comandaData);\n      console.log('✅ Comanda creata con successo:', response.data);\n      return response.data;\n    } catch (error) {\n      var _error$response;\n      console.error('❌ Errore nella creazione comanda con cavi:', error);\n      console.error('Dettagli errore:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna i dati di certificazione\n  aggiornaDatiCertificazione: async (codiceComanda, datiCertificazione) => {\n    try {\n      const response = await axiosInstance.put(`/comande/${codiceComanda}/certificazione`, datiCertificazione);\n      return response.data;\n    } catch (error) {\n      console.error('Aggiorna dati certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Assegna cavi a una comanda esistente\n  assegnaCaviAComanda: async (codiceComanda, listaIdCavi) => {\n    try {\n      console.log('🔄 Assegnazione cavi a comanda:', {\n        codiceComanda,\n        listaIdCavi\n      });\n      const response = await axiosInstance.post(`/comande/${codiceComanda}/assegna-cavi`, {\n        lista_id_cavi: listaIdCavi\n      });\n      console.log('✅ Cavi assegnati con successo:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Errore nell\\'assegnazione cavi:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Rimuove un cavo da una comanda\n  rimuoviCavoDaComanda: async (codiceComanda, idCavo) => {\n    try {\n      console.log('🔄 Rimozione cavo da comanda:', {\n        codiceComanda,\n        idCavo\n      });\n      const response = await axiosInstance.delete(`/comande/${codiceComanda}/cavi/${idCavo}`);\n      console.log('✅ Cavo rimosso con successo:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Errore nella rimozione cavo:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default comandeService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "comandeService", "getComande", "cantiereId", "params", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "data", "error", "console", "createComanda", "comandaData", "post", "getDettagliComanda", "codiceComanda", "assegnaCavi", "listaIdCavi", "lista_id_cavi", "aggiornaDatiPosa", "<PERSON><PERSON><PERSON><PERSON>", "put", "dati_posa", "aggiornaDatiCollegamento", "datiCollegamento", "dati_collegamento", "cambiaStato", "nuovoStato", "nuovo_stato", "getStatisticheComande", "getCaviComanda", "aggiornaDatiCertificazione", "datiCertificazione", "getComandeByResponsabile", "nomeResponsabile", "responsabile", "updateComanda", "log", "deleteComanda", "delete", "assignComandaToCavo", "idComanda", "idCavo", "id_cavo", "printComanda", "getCaviDisponibili", "tipoComanda", "tipo_comanda", "createComandaConCavi", "payload", "numeroCavi", "length", "cavi", "map", "id", "encodeURIComponent", "join", "_error$response", "assegnaCaviAComanda", "rimuoviCavoDaComanda"], "sources": ["C:/CMS/webapp/frontend/src/services/comandeService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst comandeService = {\r\n  // Ottiene la lista delle comande di un cantiere\r\n  getComande: async (cantiereId, params = {}) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}`, { params });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get comande error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea una nuova comanda\r\n  createComanda: async (comandaData) => {\r\n    try {\r\n      const response = await axiosInstance.post('/comande/', comandaData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene i dettagli di una comanda\r\n  getDettagliComanda: async (codiceComanda) => {\r\n    try {\r\n      const response = await axiosInstance.get(`/comande/${codiceComanda}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get dettagli comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Assegna cavi a una comanda\r\n  assegnaCavi: async (codiceComanda, listaIdCavi) => {\r\n    try {\r\n      const response = await axiosInstance.post(`/comande/${codiceComanda}/assegna-cavi`, {\r\n        lista_id_cavi: listaIdCavi\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Assegna cavi error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna i dati di posa\r\n  aggiornaDatiPosa: async (codiceComanda, datiPosa) => {\r\n    try {\r\n      const response = await axiosInstance.put(`/comande/${codiceComanda}/dati-posa`, {\r\n        dati_posa: datiPosa\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Aggiorna dati posa error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna i dati di collegamento\r\n  aggiornaDatiCollegamento: async (codiceComanda, datiCollegamento) => {\r\n    try {\r\n      const response = await axiosInstance.put(`/comande/${codiceComanda}/dati-collegamento`, {\r\n        dati_collegamento: datiCollegamento\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Aggiorna dati collegamento error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Cambia lo stato di una comanda\r\n  cambiaStato: async (codiceComanda, nuovoStato) => {\r\n    try {\r\n      const response = await axiosInstance.put(`/comande/${codiceComanda}/stato`, {\r\n        nuovo_stato: nuovoStato\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Cambia stato error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene le statistiche delle comande per un cantiere\r\n  getStatisticheComande: async (cantiereId) => {\r\n    try {\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}/statistiche`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get statistiche comande error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene i cavi assegnati a una comanda\r\n  getCaviComanda: async (codiceComanda) => {\r\n    try {\r\n      const response = await axiosInstance.get(`/comande/${codiceComanda}/cavi`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get cavi comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna i dati di posa per una comanda\r\n  aggiornaDatiPosa: async (codiceComanda, datiPosa) => {\r\n    try {\r\n      const response = await axiosInstance.post(`/comande/${codiceComanda}/dati-posa`, datiPosa);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Aggiorna dati posa error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna i dati di collegamento per una comanda\r\n  aggiornaDatiCollegamento: async (codiceComanda, datiCollegamento) => {\r\n    try {\r\n      const response = await axiosInstance.post(`/comande/${codiceComanda}/dati-collegamento`, datiCollegamento);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Aggiorna dati collegamento error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna i dati di certificazione per una comanda\r\n  aggiornaDatiCertificazione: async (codiceComanda, datiCertificazione) => {\r\n    try {\r\n      const response = await axiosInstance.post(`/comande/${codiceComanda}/dati-certificazione`, datiCertificazione);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Aggiorna dati certificazione error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene le comande per un responsabile specifico\r\n  getComandeByResponsabile: async (cantiereId, nomeResponsabile) => {\r\n    try {\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}`, {\r\n        params: { responsabile: nomeResponsabile }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get comande by responsabile error:', error);\r\n      // Non lanciare errore, ritorna array vuoto\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Aggiorna una comanda esistente (solo campi modificabili)\r\n  updateComanda: async (codiceComanda, comandaData) => {\r\n    try {\r\n      console.log('🔄 Aggiornamento comanda:', { codiceComanda, comandaData });\r\n\r\n      const response = await axiosInstance.put(`/comande/${codiceComanda}`, comandaData);\r\n\r\n      console.log('✅ Comanda aggiornata:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Errore aggiornamento comanda:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Elimina una comanda\r\n  deleteComanda: async (codiceComanda) => {\r\n    try {\r\n      const response = await axiosInstance.delete(`/comande/${codiceComanda}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Assegna una comanda a un cavo\r\n  assignComandaToCavo: async (cantiereId, idComanda, idCavo) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/comande/${cantiereIdNum}/${idComanda}/assign`, {\r\n        id_cavo: idCavo\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Assign comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Genera PDF di una comanda\r\n  printComanda: async (cantiereId, idComanda) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/comande/${cantiereIdNum}/${idComanda}/pdf`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Print comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene i cavi disponibili per un tipo di comanda\r\n  getCaviDisponibili: async (cantiereId, tipoComanda) => {\r\n    try {\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}/cavi-disponibili`, {\r\n        params: { tipo_comanda: tipoComanda }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get cavi disponibili error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea una comanda con cavi pre-selezionati\r\n  createComandaConCavi: async (cantiereId, comandaData, listaIdCavi) => {\r\n    try {\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Prepara il payload completo con i dati della comanda e la lista dei cavi\r\n      const payload = {\r\n        ...comandaData,\r\n        lista_id_cavi: listaIdCavi\r\n      };\r\n\r\n      console.log('🚀 Creazione comanda con cavi:', {\r\n        cantiereId: cantiereIdNum,\r\n        tipoComanda: comandaData.tipo_comanda,\r\n        responsabile: comandaData.responsabile,\r\n        numeroCavi: listaIdCavi.length,\r\n        cavi: listaIdCavi\r\n      });\r\n\r\n      const response = await axiosInstance.post(`/comande/cantiere/${cantiereIdNum}/crea-con-cavi?${listaIdCavi.map(id => `lista_id_cavi=${encodeURIComponent(id)}`).join('&')}`, comandaData);\r\n\r\n      console.log('✅ Comanda creata con successo:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Errore nella creazione comanda con cavi:', error);\r\n      console.error('Dettagli errore:', error.response?.data);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna i dati di certificazione\r\n  aggiornaDatiCertificazione: async (codiceComanda, datiCertificazione) => {\r\n    try {\r\n      const response = await axiosInstance.put(`/comande/${codiceComanda}/certificazione`, datiCertificazione);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Aggiorna dati certificazione error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Assegna cavi a una comanda esistente\r\n  assegnaCaviAComanda: async (codiceComanda, listaIdCavi) => {\r\n    try {\r\n      console.log('🔄 Assegnazione cavi a comanda:', { codiceComanda, listaIdCavi });\r\n\r\n      const response = await axiosInstance.post(`/comande/${codiceComanda}/assegna-cavi`, {\r\n        lista_id_cavi: listaIdCavi\r\n      });\r\n\r\n      console.log('✅ Cavi assegnati con successo:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Errore nell\\'assegnazione cavi:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Rimuove un cavo da una comanda\r\n  rimuoviCavoDaComanda: async (codiceComanda, idCavo) => {\r\n    try {\r\n      console.log('🔄 Rimozione cavo da comanda:', { codiceComanda, idCavo });\r\n\r\n      const response = await axiosInstance.delete(`/comande/${codiceComanda}/cavi/${idCavo}`);\r\n\r\n      console.log('✅ Cavo rimosso con successo:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Errore nella rimozione cavo:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default comandeService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,cAAc,GAAG;EACrB;EACAC,UAAU,EAAE,MAAAA,CAAOC,UAAU,EAAEC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC7C,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,qBAAqBL,aAAa,EAAE,EAAE;QAAED;MAAO,CAAC,CAAC;MAC1F,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,aAAa,EAAE,MAAOC,WAAW,IAAK;IACpC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,WAAW,EAAED,WAAW,CAAC;MACnE,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAK,kBAAkB,EAAE,MAAOC,aAAa,IAAK;IAC3C,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,YAAYQ,aAAa,EAAE,CAAC;MACrE,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAO,WAAW,EAAE,MAAAA,CAAOD,aAAa,EAAEE,WAAW,KAAK;IACjD,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,YAAYE,aAAa,eAAe,EAAE;QAClFG,aAAa,EAAED;MACjB,CAAC,CAAC;MACF,OAAOX,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAU,gBAAgB,EAAE,MAAAA,CAAOJ,aAAa,EAAEK,QAAQ,KAAK;IACnD,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMV,aAAa,CAACyB,GAAG,CAAC,YAAYN,aAAa,YAAY,EAAE;QAC9EO,SAAS,EAAEF;MACb,CAAC,CAAC;MACF,OAAOd,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAc,wBAAwB,EAAE,MAAAA,CAAOR,aAAa,EAAES,gBAAgB,KAAK;IACnE,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMV,aAAa,CAACyB,GAAG,CAAC,YAAYN,aAAa,oBAAoB,EAAE;QACtFU,iBAAiB,EAAED;MACrB,CAAC,CAAC;MACF,OAAOlB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAiB,WAAW,EAAE,MAAAA,CAAOX,aAAa,EAAEY,UAAU,KAAK;IAChD,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMV,aAAa,CAACyB,GAAG,CAAC,YAAYN,aAAa,QAAQ,EAAE;QAC1Ea,WAAW,EAAED;MACf,CAAC,CAAC;MACF,OAAOrB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAoB,qBAAqB,EAAE,MAAO7B,UAAU,IAAK;IAC3C,IAAI;MACF,MAAME,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,qBAAqBL,aAAa,cAAc,CAAC;MAC1F,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAqB,cAAc,EAAE,MAAOf,aAAa,IAAK;IACvC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,YAAYQ,aAAa,OAAO,CAAC;MAC1E,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAU,gBAAgB,EAAE,MAAAA,CAAOJ,aAAa,EAAEK,QAAQ,KAAK;IACnD,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,YAAYE,aAAa,YAAY,EAAEK,QAAQ,CAAC;MAC1F,OAAOd,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAc,wBAAwB,EAAE,MAAAA,CAAOR,aAAa,EAAES,gBAAgB,KAAK;IACnE,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,YAAYE,aAAa,oBAAoB,EAAES,gBAAgB,CAAC;MAC1G,OAAOlB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAsB,0BAA0B,EAAE,MAAAA,CAAOhB,aAAa,EAAEiB,kBAAkB,KAAK;IACvE,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,YAAYE,aAAa,sBAAsB,EAAEiB,kBAAkB,CAAC;MAC9G,OAAO1B,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAwB,wBAAwB,EAAE,MAAAA,CAAOjC,UAAU,EAAEkC,gBAAgB,KAAK;IAChE,IAAI;MACF,MAAMhC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,qBAAqBL,aAAa,EAAE,EAAE;QAC7ED,MAAM,EAAE;UAAEkC,YAAY,EAAED;QAAiB;MAC3C,CAAC,CAAC;MACF,OAAO5B,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D;MACA,OAAO,EAAE;IACX;EACF,CAAC;EAED;EACA2B,aAAa,EAAE,MAAAA,CAAOrB,aAAa,EAAEH,WAAW,KAAK;IACnD,IAAI;MACFF,OAAO,CAAC2B,GAAG,CAAC,2BAA2B,EAAE;QAAEtB,aAAa;QAAEH;MAAY,CAAC,CAAC;MAExE,MAAMN,QAAQ,GAAG,MAAMV,aAAa,CAACyB,GAAG,CAAC,YAAYN,aAAa,EAAE,EAAEH,WAAW,CAAC;MAElFF,OAAO,CAAC2B,GAAG,CAAC,uBAAuB,EAAE/B,QAAQ,CAACE,IAAI,CAAC;MACnD,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACA6B,aAAa,EAAE,MAAOvB,aAAa,IAAK;IACtC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMV,aAAa,CAAC2C,MAAM,CAAC,YAAYxB,aAAa,EAAE,CAAC;MACxE,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACA+B,mBAAmB,EAAE,MAAAA,CAAOxC,UAAU,EAAEyC,SAAS,EAAEC,MAAM,KAAK;IAC5D,IAAI;MACF;MACA,MAAMxC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,YAAYX,aAAa,IAAIuC,SAAS,SAAS,EAAE;QACzFE,OAAO,EAAED;MACX,CAAC,CAAC;MACF,OAAOpC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAmC,YAAY,EAAE,MAAAA,CAAO5C,UAAU,EAAEyC,SAAS,KAAK;IAC7C,IAAI;MACF;MACA,MAAMvC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,YAAYL,aAAa,IAAIuC,SAAS,MAAM,CAAC;MACtF,OAAOnC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAoC,kBAAkB,EAAE,MAAAA,CAAO7C,UAAU,EAAE8C,WAAW,KAAK;IACrD,IAAI;MACF,MAAM5C,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,qBAAqBL,aAAa,mBAAmB,EAAE;QAC9FD,MAAM,EAAE;UAAE8C,YAAY,EAAED;QAAY;MACtC,CAAC,CAAC;MACF,OAAOxC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAuC,oBAAoB,EAAE,MAAAA,CAAOhD,UAAU,EAAEY,WAAW,EAAEK,WAAW,KAAK;IACpE,IAAI;MACF,MAAMf,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,MAAMiD,OAAO,GAAG;QACd,GAAGrC,WAAW;QACdM,aAAa,EAAED;MACjB,CAAC;MAEDP,OAAO,CAAC2B,GAAG,CAAC,gCAAgC,EAAE;QAC5CrC,UAAU,EAAEE,aAAa;QACzB4C,WAAW,EAAElC,WAAW,CAACmC,YAAY;QACrCZ,YAAY,EAAEvB,WAAW,CAACuB,YAAY;QACtCe,UAAU,EAAEjC,WAAW,CAACkC,MAAM;QAC9BC,IAAI,EAAEnC;MACR,CAAC,CAAC;MAEF,MAAMX,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,qBAAqBX,aAAa,kBAAkBe,WAAW,CAACoC,GAAG,CAACC,EAAE,IAAI,iBAAiBC,kBAAkB,CAACD,EAAE,CAAC,EAAE,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE5C,WAAW,CAAC;MAExLF,OAAO,CAAC2B,GAAG,CAAC,gCAAgC,EAAE/B,QAAQ,CAACE,IAAI,CAAC;MAC5D,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAgD,eAAA;MACd/C,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClEC,OAAO,CAACD,KAAK,CAAC,kBAAkB,GAAAgD,eAAA,GAAEhD,KAAK,CAACH,QAAQ,cAAAmD,eAAA,uBAAdA,eAAA,CAAgBjD,IAAI,CAAC;MACvD,MAAMC,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAsB,0BAA0B,EAAE,MAAAA,CAAOhB,aAAa,EAAEiB,kBAAkB,KAAK;IACvE,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMV,aAAa,CAACyB,GAAG,CAAC,YAAYN,aAAa,iBAAiB,EAAEiB,kBAAkB,CAAC;MACxG,OAAO1B,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAiD,mBAAmB,EAAE,MAAAA,CAAO3C,aAAa,EAAEE,WAAW,KAAK;IACzD,IAAI;MACFP,OAAO,CAAC2B,GAAG,CAAC,iCAAiC,EAAE;QAAEtB,aAAa;QAAEE;MAAY,CAAC,CAAC;MAE9E,MAAMX,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,YAAYE,aAAa,eAAe,EAAE;QAClFG,aAAa,EAAED;MACjB,CAAC,CAAC;MAEFP,OAAO,CAAC2B,GAAG,CAAC,gCAAgC,EAAE/B,QAAQ,CAACE,IAAI,CAAC;MAC5D,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAkD,oBAAoB,EAAE,MAAAA,CAAO5C,aAAa,EAAE2B,MAAM,KAAK;IACrD,IAAI;MACFhC,OAAO,CAAC2B,GAAG,CAAC,+BAA+B,EAAE;QAAEtB,aAAa;QAAE2B;MAAO,CAAC,CAAC;MAEvE,MAAMpC,QAAQ,GAAG,MAAMV,aAAa,CAAC2C,MAAM,CAAC,YAAYxB,aAAa,SAAS2B,MAAM,EAAE,CAAC;MAEvFhC,OAAO,CAAC2B,GAAG,CAAC,8BAA8B,EAAE/B,QAAQ,CAACE,IAAI,CAAC;MAC1D,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeX,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}