{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { Box, Typography, Button, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, List, ListItem, ListItemText, Stack, MenuItem, Divider } from '@mui/material';\nimport { Add as AddIcon, Assignment as AssignIcon, Person as PersonIcon, CheckCircle as CheckCircleIcon, Verified as VerifiedIcon, People as PeopleIcon, Construction as ConstructionIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\nimport InserimentoMetriDialog from './InserimentoMetriDialog';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    note_capo_cantiere: ''\n  });\n\n  // Stati per dialog inserimento metri\n  const [openInserimentoMetri, setOpenInserimentoMetri] = useState(false);\n  const [comandaPerMetri, setComandaPerMetri] = useState(null);\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      console.log('🔄 Caricamento comande per cantiere:', cantiereId);\n      const comandeData = await comandeService.getComande(cantiereId);\n      console.log('📋 Dati comande ricevuti:', comandeData);\n      console.log('📋 Tipo dati:', typeof comandeData, 'Array?', Array.isArray(comandeData));\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comandeData)) {\n        comandeArray = comandeData;\n      } else if (comandeData && Array.isArray(comandeData.comande)) {\n        comandeArray = comandeData.comande;\n      } else if (comandeData && Array.isArray(comandeData.data)) {\n        comandeArray = comandeData.data;\n      }\n      console.log('📋 Array comande finale:', comandeArray, 'Lunghezza:', comandeArray.length);\n      setAllComande(comandeArray);\n    } catch (err) {\n      console.error('Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      console.log('📊 Statistiche ricevute:', stats);\n      setStatistiche(stats);\n    } catch (err) {\n      var _err$response;\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    const initializeData = async () => {\n      if (cantiereId) {\n        setLoading(true);\n        try {\n          await Promise.all([loadResponsabili(), loadComande(), loadStatistiche()]);\n        } catch (err) {\n          console.error('Errore nel caricamento iniziale:', err);\n        } finally {\n          setLoading(false);\n        }\n      }\n    };\n    initializeData();\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n        handleOpenComandaDialog('view', comandaTrovata);\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (mode, comanda = null) => {\n    setDialogModeComanda(mode);\n    setSelectedComanda(comanda);\n    if (mode === 'edit' && comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenComandaDialog(true);\n  };\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmitComanda = async () => {\n    try {\n      if (dialogModeComanda === 'edit') {\n        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n        handleCloseComandaDialog();\n        await loadResponsabili(); // Ricarica per aggiornare le comande\n        await loadComande();\n        await loadStatistiche();\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDeleteComanda = async codiceComanda => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  // Gestione inserimento metri\n  const handleOpenInserimentoMetri = comanda => {\n    setComandaPerMetri(comanda);\n    setOpenInserimentoMetri(true);\n  };\n  const handleCloseInserimentoMetri = () => {\n    setOpenInserimentoMetri(false);\n    setComandaPerMetri(null);\n  };\n  const handleSuccessInserimentoMetri = async message => {\n    console.log('✅ Successo inserimento metri:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([loadResponsabili(), loadComande(), loadStatistiche()]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing/Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  if (loading || loadingComande) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600,\n          color: 'primary.main'\n        },\n        children: cantiereName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 9\n    }, this), searchingComanda && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: [\"\\uD83D\\uDD0D Ricerca comanda \", searchingComanda, \" in corso...\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 9\n    }, this), statistiche && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        bgcolor: 'grey.50'\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 4,\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.responsabili_attivi || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            color: \"info\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.totale_comande || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Totale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            color: \"warning\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_in_corso || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"In Corso\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n            color: \"success\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_completate || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              bgcolor: statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.8 ? 'success.main' : statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.5 ? 'warning.main' : 'error.main',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              fontWeight: \"bold\",\n              color: \"white\",\n              children: [statistiche.totale_comande > 0 ? Math.round(statistiche.comande_completate / statistiche.totale_comande * 100) : 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"medium\",\n              sx: {\n                lineHeight: 1\n              },\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [statistiche.comande_create || 0, \" create\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Gestione Responsabili e Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            gap: 2,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 28\n              }, this),\n              onClick: () => setOpenResponsabiliPopup(true),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#f5f7fa',\n                color: '#2196f3',\n                border: '1px solid #2196f3',\n                '&:hover': {\n                  backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                  borderColor: '#1976d2'\n                }\n              },\n              children: \"Lista Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 28\n              }, this),\n              onClick: () => handleOpenResponsabileDialog('create'),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#2196f3',\n                color: 'white',\n                '&:hover': {\n                  backgroundColor: '#1976d2'\n                }\n              },\n              children: \"Crea Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this), loadingComande ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          py: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this) : allComande.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 0,\n          sx: {\n            p: 6,\n            textAlign: 'center',\n            backgroundColor: 'grey.50',\n            border: '1px dashed',\n            borderColor: 'grey.300'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            sx: {\n              fontSize: 48,\n              color: 'grey.400',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: \"Nessuna comanda disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Crea la prima comanda per iniziare a gestire i lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 28\n            }, this),\n            onClick: () => setOpenCreaConCavi(true),\n            sx: {\n              textTransform: 'none',\n              backgroundColor: '#2196f3',\n              color: 'white',\n              '&:hover': {\n                backgroundColor: '#1976d2'\n              }\n            },\n            children: \"Crea Prima Comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ComandeListTable, {\n          comande: allComande,\n          onEditComanda: handleOpenComandaDialog.bind(null, 'edit'),\n          onDeleteComanda: handleDeleteComanda,\n          onInserimentoMetri: handleOpenInserimentoMetri,\n          loading: loadingComande\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 676,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 681,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 717,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openComandaDialog,\n      onClose: handleCloseComandaDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: \"Modifica Comanda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 749,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 748,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            select: true,\n            label: \"Tipo Comanda\",\n            value: formDataComanda.tipo_comanda,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              tipo_comanda: e.target.value\n            }),\n            margin: \"normal\",\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"POSA\",\n              children: \"Posa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_PARTENZA\",\n              children: \"Collegamento Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_ARRIVO\",\n              children: \"Collegamento Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"CERTIFICAZIONE\",\n              children: \"Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"TESTING\",\n              children: \"Testing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Descrizione\",\n            value: formDataComanda.descrizione,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              descrizione: e.target.value\n            }),\n            margin: \"normal\",\n            multiline: true,\n            rows: 3,\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Responsabile\",\n            value: formDataComanda.responsabile,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Note Capo Cantiere\",\n            value: formDataComanda.note_capo_cantiere,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              note_capo_cantiere: e.target.value\n            }),\n            margin: \"normal\",\n            multiline: true,\n            rows: 2,\n            helperText: \"Istruzioni specifiche per il responsabile\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Data Scadenza\",\n            type: \"date\",\n            value: formDataComanda.data_scadenza,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              data_scadenza: e.target.value\n            }),\n            margin: \"normal\",\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 805,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseComandaDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitComanda,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: \"Salva Modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 818,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 739,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: (response, successMessage) => {\n        console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n        // Mostra messaggio di successo se fornito\n        if (successMessage) {\n          // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n          console.log('📢 Successo:', successMessage);\n        }\n\n        // Ricarica tutti i dati per aggiornare l'interfaccia\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n        console.log('✅ Interfaccia aggiornata');\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 840,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ResponsabiliListPopup, {\n      open: openResponsabiliPopup,\n      onClose: () => setOpenResponsabiliPopup(false),\n      responsabili: responsabili,\n      comandePerResponsabile: comandePerResponsabile,\n      onEditResponsabile: responsabile => {\n        setOpenResponsabiliPopup(false);\n        handleOpenResponsabileDialog('edit', responsabile);\n      },\n      onDeleteResponsabile: async idResponsabile => {\n        await handleDeleteResponsabile(idResponsabile);\n        setOpenResponsabiliPopup(false);\n      },\n      loading: loadingResponsabili,\n      error: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 864,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(InserimentoMetriDialog, {\n      open: openInserimentoMetri,\n      onClose: handleCloseInserimentoMetri,\n      comanda: comandaPerMetri,\n      onSuccess: handleSuccessInserimentoMetri\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 882,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 460,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"1nCJLogCYo5BCWX8l5bTwFEVVH8=\", false, function () {\n  return [useSearchParams];\n});\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "List", "ListItem", "ListItemText", "<PERSON><PERSON>", "MenuItem", "Divider", "Add", "AddIcon", "Assignment", "AssignIcon", "Person", "PersonIcon", "CheckCircle", "CheckCircleIcon", "Verified", "VerifiedIcon", "People", "PeopleIcon", "Construction", "ConstructionIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "ResponsabiliListPopup", "ComandeListTable", "InserimentoMetriDialog", "jsxDEV", "_jsxDEV", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "searchParams", "setSearchParams", "loading", "setLoading", "error", "setError", "searchingComanda", "setSearchingComanda", "statistiche", "setStatistiche", "allComande", "setAllComande", "loadingComande", "setLoadingComande", "openResponsabiliPopup", "setOpenResponsabiliPopup", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "openComandaDialog", "setOpenComandaDialog", "dialogModeComanda", "setDialogModeComanda", "selectedComanda", "setSelectedComanda", "formDataComanda", "setFormDataComanda", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "note_capo_cantiere", "openInserimentoMetri", "setOpenInserimentoMetri", "comandaPerMetri", "setComandaPerMetri", "loadComande", "console", "log", "comandeData", "getComande", "Array", "isArray", "comandeArray", "comande", "data", "length", "err", "loadStatistiche", "stats", "getStatisticheComande", "_err$response", "response", "message", "loadResponsabili", "getResponsabiliCantiere", "loadComandePerResponsabili", "_err$response2", "_err$response2$data", "errorMessage", "detail", "initializeData", "Promise", "all", "comandaParam", "get", "Object", "keys", "comandaTrovata", "comandeResp", "id_responsabile", "find", "c", "codice_comanda", "handleOpenComandaDialog", "setTimeout", "prev", "newParams", "URLSearchParams", "delete", "warn", "for<PERSON>ach", "resp", "cmd", "responsabiliList", "comandeMap", "getComandeByResponsabile", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "comanda", "handleCloseComandaDialog", "handleSubmitComanda", "updateComanda", "handleDeleteComanda", "codiceComanda", "deleteComanda", "handleOpenInserimentoMetri", "handleCloseInserimentoMetri", "handleSuccessInserimentoMetri", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "variant", "fontWeight", "color", "severity", "bgcolor", "direction", "spacing", "flexWrap", "fontSize", "lineHeight", "responsabili_attivi", "totale_comande", "comande_in_corso", "comande_completate", "width", "height", "borderRadius", "Math", "round", "comande_create", "gap", "startIcon", "onClick", "textTransform", "px", "py", "backgroundColor", "border", "borderColor", "elevation", "textAlign", "gutterBottom", "onEditComanda", "bind", "onDeleteComanda", "onInserimentoMetri", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "pt", "label", "value", "onChange", "e", "target", "margin", "required", "type", "helperText", "select", "multiline", "rows", "InputLabelProps", "shrink", "onSuccess", "successMessage", "onEditResponsabile", "onDeleteResponsabile", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  List,\n  ListItem,\n  ListItemText,\n  Stack,\n  MenuItem,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Assignment as AssignIcon,\n  Person as PersonIcon,\n  CheckCircle as CheckCircleIcon,\n  Verified as VerifiedIcon,\n  People as PeopleIcon,\n  Construction as ConstructionIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\nimport InserimentoMetriDialog from './InserimentoMetriDialog';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    note_capo_cantiere: ''\n  });\n\n  // Stati per dialog inserimento metri\n  const [openInserimentoMetri, setOpenInserimentoMetri] = useState(false);\n  const [comandaPerMetri, setComandaPerMetri] = useState(null);\n\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      console.log('🔄 Caricamento comande per cantiere:', cantiereId);\n      const comandeData = await comandeService.getComande(cantiereId);\n      console.log('📋 Dati comande ricevuti:', comandeData);\n      console.log('📋 Tipo dati:', typeof comandeData, 'Array?', Array.isArray(comandeData));\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comandeData)) {\n        comandeArray = comandeData;\n      } else if (comandeData && Array.isArray(comandeData.comande)) {\n        comandeArray = comandeData.comande;\n      } else if (comandeData && Array.isArray(comandeData.data)) {\n        comandeArray = comandeData.data;\n      }\n\n      console.log('📋 Array comande finale:', comandeArray, 'Lunghezza:', comandeArray.length);\n      setAllComande(comandeArray);\n    } catch (err) {\n      console.error('Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      console.log('📊 Statistiche ricevute:', stats);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', err.response?.data || err.message);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    const initializeData = async () => {\n      if (cantiereId) {\n        setLoading(true);\n        try {\n          await Promise.all([\n            loadResponsabili(),\n            loadComande(),\n            loadStatistiche()\n          ]);\n        } catch (err) {\n          console.error('Errore nel caricamento iniziale:', err);\n        } finally {\n          setLoading(false);\n        }\n      }\n    };\n\n    initializeData();\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n        handleOpenComandaDialog('view', comandaTrovata);\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (mode, comanda = null) => {\n    setDialogModeComanda(mode);\n    setSelectedComanda(comanda);\n\n    if (mode === 'edit' && comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenComandaDialog(true);\n  };\n\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmitComanda = async () => {\n    try {\n      if (dialogModeComanda === 'edit') {\n        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n        handleCloseComandaDialog();\n        await loadResponsabili(); // Ricarica per aggiornare le comande\n        await loadComande();\n        await loadStatistiche();\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDeleteComanda = async (codiceComanda) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  // Gestione inserimento metri\n  const handleOpenInserimentoMetri = (comanda) => {\n    setComandaPerMetri(comanda);\n    setOpenInserimentoMetri(true);\n  };\n\n  const handleCloseInserimentoMetri = () => {\n    setOpenInserimentoMetri(false);\n    setComandaPerMetri(null);\n  };\n\n  const handleSuccessInserimentoMetri = async (message) => {\n    console.log('✅ Successo inserimento metri:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([\n      loadResponsabili(),\n      loadComande(),\n      loadStatistiche()\n    ]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing/Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n\n\n  if (loading || loadingComande) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n          {cantiereName}\n        </Typography>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {searchingComanda && (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          🔍 Ricerca comanda {searchingComanda} in corso...\n        </Alert>\n      )}\n\n      {/* Statistiche in stile Visualizza Cavi */}\n      {statistiche && (\n        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n          <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n            {/* Responsabili */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <PersonIcon color=\"primary\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.responsabili_attivi || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Responsabili\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Totale Comande */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <AssignIcon color=\"info\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.totale_comande || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Totale\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* In Corso */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <CheckCircleIcon color=\"warning\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_in_corso || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  In Corso\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Completate */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <VerifiedIcon color=\"success\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_completate || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Completate\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Percentuale completamento */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <Box sx={{\n                width: 32,\n                height: 32,\n                borderRadius: '50%',\n                bgcolor: (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.8 ? 'success.main' :\n                         (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.5 ? 'warning.main' : 'error.main',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n                  {statistiche.totale_comande > 0 ? Math.round((statistiche.comande_completate / statistiche.totale_comande) * 100) : 0}%\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n                  Completamento\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {statistiche.comande_create || 0} create\n                </Typography>\n              </Box>\n            </Stack>\n          </Stack>\n        </Paper>\n      )}\n\n      {/* Sezione Comande - Elemento Principale */}\n      <Box>\n        <Box>\n          {/* Toolbar Comande */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Gestione Responsabili e Comande\n            </Typography>\n            <Box display=\"flex\" gap={2}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<PeopleIcon />}\n                onClick={() => setOpenResponsabiliPopup(true)}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#f5f7fa',\n                  color: '#2196f3',\n                  border: '1px solid #2196f3',\n                  '&:hover': {\n                    backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                    borderColor: '#1976d2'\n                  }\n                }}\n              >\n                Lista Responsabili\n              </Button>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => handleOpenResponsabileDialog('create')}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Responsabile\n              </Button>\n            </Box>\n          </Box>\n\n          {/* Lista Comande in stile tabella */}\n          {loadingComande ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : allComande.length === 0 ? (\n            <Paper\n              elevation={0}\n              sx={{\n                p: 6,\n                textAlign: 'center',\n                backgroundColor: 'grey.50',\n                border: '1px dashed',\n                borderColor: 'grey.300'\n              }}\n            >\n              <AssignIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                Nessuna comanda disponibile\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                Crea la prima comanda per iniziare a gestire i lavori\n              </Typography>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => setOpenCreaConCavi(true)}\n                sx={{\n                  textTransform: 'none',\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Prima Comanda\n              </Button>\n            </Paper>\n          ) : (\n            <ComandeListTable\n              comande={allComande}\n              onEditComanda={handleOpenComandaDialog.bind(null, 'edit')}\n              onDeleteComanda={handleDeleteComanda}\n              onInserimentoMetri={handleOpenInserimentoMetri}\n              loading={loadingComande}\n            />\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per visualizzazione/modifica comanda */}\n      <Dialog\n        open={openComandaDialog}\n        onClose={handleCloseComandaDialog}\n        maxWidth=\"md\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            Modifica Comanda\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              select\n              label=\"Tipo Comanda\"\n              value={formDataComanda.tipo_comanda}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, tipo_comanda: e.target.value })}\n              margin=\"normal\"\n              sx={{ mb: 2 }}\n            >\n              <MenuItem value=\"POSA\">Posa</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n              <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n              <MenuItem value=\"TESTING\">Testing</MenuItem>\n            </TextField>\n\n            <TextField\n              fullWidth\n              label=\"Descrizione\"\n              value={formDataComanda.descrizione}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, descrizione: e.target.value })}\n              margin=\"normal\"\n              multiline\n              rows={3}\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Responsabile\"\n              value={formDataComanda.responsabile}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Note Capo Cantiere\"\n              value={formDataComanda.note_capo_cantiere}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, note_capo_cantiere: e.target.value })}\n              margin=\"normal\"\n              multiline\n              rows={2}\n              helperText=\"Istruzioni specifiche per il responsabile\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Data Scadenza\"\n              type=\"date\"\n              value={formDataComanda.data_scadenza}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, data_scadenza: e.target.value })}\n              margin=\"normal\"\n              InputLabelProps={{\n                shrink: true,\n              }}\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseComandaDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitComanda}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            Salva Modifiche\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={(response, successMessage) => {\n          console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n          // Mostra messaggio di successo se fornito\n          if (successMessage) {\n            // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n            console.log('📢 Successo:', successMessage);\n          }\n\n          // Ricarica tutti i dati per aggiornare l'interfaccia\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n\n          console.log('✅ Interfaccia aggiornata');\n        }}\n      />\n\n      {/* Popup Lista Responsabili */}\n      <ResponsabiliListPopup\n        open={openResponsabiliPopup}\n        onClose={() => setOpenResponsabiliPopup(false)}\n        responsabili={responsabili}\n        comandePerResponsabile={comandePerResponsabile}\n        onEditResponsabile={(responsabile) => {\n          setOpenResponsabiliPopup(false);\n          handleOpenResponsabileDialog('edit', responsabile);\n        }}\n        onDeleteResponsabile={async (idResponsabile) => {\n          await handleDeleteResponsabile(idResponsabile);\n          setOpenResponsabiliPopup(false);\n        }}\n        loading={loadingResponsabili}\n        error={error}\n      />\n\n      {/* Dialog Inserimento Metri */}\n      <InserimentoMetriDialog\n        open={openInserimentoMetri}\n        onClose={handleCloseInserimentoMetri}\n        comanda={comandaPerMetri}\n        onSuccess={handleSuccessInserimentoMetri}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,KAAK,EACLC,QAAQ,EACRC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,UAAU,IAAIC,UAAU,EACxBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,sBAAsB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9C,eAAe,CAAC,CAAC;;EAEzD;EACA,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC6D,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC+D,eAAe,EAAEC,kBAAkB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACiE,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACqE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGtE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACuE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACyE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG1E,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAAC2E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC6E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9E,QAAQ,CAAC;IAC/D+E,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrF,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACpE,MAAM,CAACsF,eAAe,EAAEC,kBAAkB,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwF,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAC;IACrD0F,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACiG,eAAe,EAAEC,kBAAkB,CAAC,GAAGlG,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAMmG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFvC,iBAAiB,CAAC,IAAI,CAAC;MACvBwC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEzD,UAAU,CAAC;MAC/D,MAAM0D,WAAW,GAAG,MAAMnE,cAAc,CAACoE,UAAU,CAAC3D,UAAU,CAAC;MAC/DwD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,WAAW,CAAC;MACrDF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAOC,WAAW,EAAE,QAAQ,EAAEE,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,CAAC;;MAEtF;MACA,IAAII,YAAY,GAAG,EAAE;MACrB,IAAIF,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE;QAC9BI,YAAY,GAAGJ,WAAW;MAC5B,CAAC,MAAM,IAAIA,WAAW,IAAIE,KAAK,CAACC,OAAO,CAACH,WAAW,CAACK,OAAO,CAAC,EAAE;QAC5DD,YAAY,GAAGJ,WAAW,CAACK,OAAO;MACpC,CAAC,MAAM,IAAIL,WAAW,IAAIE,KAAK,CAACC,OAAO,CAACH,WAAW,CAACM,IAAI,CAAC,EAAE;QACzDF,YAAY,GAAGJ,WAAW,CAACM,IAAI;MACjC;MAEAR,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEK,YAAY,EAAE,YAAY,EAAEA,YAAY,CAACG,MAAM,CAAC;MACxFnD,aAAa,CAACgD,YAAY,CAAC;IAC7B,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZV,OAAO,CAACjD,KAAK,CAAC,iCAAiC,EAAE2D,GAAG,CAAC;MACrD1D,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRQ,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMmD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFX,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEzD,UAAU,CAAC;MACnE,MAAMoE,KAAK,GAAG,MAAM7E,cAAc,CAAC8E,qBAAqB,CAACrE,UAAU,CAAC;MACpEwD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEW,KAAK,CAAC;MAC9CxD,cAAc,CAACwD,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MAAA,IAAAI,aAAA;MACZd,OAAO,CAACjD,KAAK,CAAC,6CAA6C,EAAE2D,GAAG,CAAC;MACjEV,OAAO,CAACjD,KAAK,CAAC,oBAAoB,EAAE,EAAA+D,aAAA,GAAAJ,GAAG,CAACK,QAAQ,cAAAD,aAAA,uBAAZA,aAAA,CAAcN,IAAI,KAAIE,GAAG,CAACM,OAAO,CAAC;IACxE;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFjD,sBAAsB,CAAC,IAAI,CAAC;MAC5BhB,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMwD,IAAI,GAAG,MAAMxE,mBAAmB,CAACkF,uBAAuB,CAAC1E,UAAU,CAAC;MAC1EsB,eAAe,CAAC0C,IAAI,IAAI,EAAE,CAAC;MAC3B,MAAMW,0BAA0B,CAACX,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOE,GAAG,EAAE;MAAA,IAAAU,cAAA,EAAAC,mBAAA;MACZrB,OAAO,CAACjD,KAAK,CAAC,0CAA0C,EAAE2D,GAAG,CAAC;MAC9D,MAAMY,YAAY,GAAG,EAAAF,cAAA,GAAAV,GAAG,CAACK,QAAQ,cAAAK,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,uBAAlBA,mBAAA,CAAoBE,MAAM,KAAIb,GAAG,CAACM,OAAO,IAAI,yCAAyC;MAC3GhE,QAAQ,CAAC,4CAA4CsE,YAAY,EAAE,CAAC;IACtE,CAAC,SAAS;MACRtD,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAID;EACAnE,SAAS,CAAC,MAAM;IACd,MAAM2H,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAIhF,UAAU,EAAE;QACdM,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACF,MAAM2E,OAAO,CAACC,GAAG,CAAC,CAChBT,gBAAgB,CAAC,CAAC,EAClBlB,WAAW,CAAC,CAAC,EACbY,eAAe,CAAC,CAAC,CAClB,CAAC;QACJ,CAAC,CAAC,OAAOD,GAAG,EAAE;UACZV,OAAO,CAACjD,KAAK,CAAC,kCAAkC,EAAE2D,GAAG,CAAC;QACxD,CAAC,SAAS;UACR5D,UAAU,CAAC,KAAK,CAAC;QACnB;MACF;IACF,CAAC;IAED0E,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAChF,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB;EACA3C,SAAS,CAAC,MAAM;IACd,MAAM8H,YAAY,GAAGhF,YAAY,CAACiF,GAAG,CAAC,SAAS,CAAC;IAChD5B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE0B,YAAY,CAAC;IAChE3B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAC5BpC,YAAY,EAAEA,YAAY,CAAC4C,MAAM;MACjCxC,sBAAsB,EAAE4D,MAAM,CAACC,IAAI,CAAC7D,sBAAsB,CAAC,CAACwC,MAAM;MAClE5D,OAAO;MACPkB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI4D,YAAY,IAAIA,YAAY,KAAK1E,gBAAgB,EAAE;MACrDC,mBAAmB,CAACyE,YAAY,CAAC;IACnC;IAEA,IAAIA,YAAY,IAAI9D,YAAY,CAAC4C,MAAM,GAAG,CAAC,IAAIoB,MAAM,CAACC,IAAI,CAAC7D,sBAAsB,CAAC,CAACwC,MAAM,GAAG,CAAC,EAAE;MAC7FT,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,IAAI8B,cAAc,GAAG,IAAI;MAEzB,KAAK,MAAMvC,YAAY,IAAI3B,YAAY,EAAE;QACvC,MAAMmE,WAAW,GAAG/D,sBAAsB,CAACuB,YAAY,CAACyC,eAAe,CAAC,IAAI,EAAE;QAC9EjC,OAAO,CAACC,GAAG,CAAC,mBAAmBT,YAAY,CAACb,iBAAiB,KAAKqD,WAAW,CAACvB,MAAM,UAAU,CAAC;QAC/FsB,cAAc,GAAGC,WAAW,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAKT,YAAY,CAAC;QACzE,IAAII,cAAc,EAAE;UAClB/B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8B,cAAc,CAAC;UACjD;QACF;MACF;MAEA,IAAIA,cAAc,EAAE;QAClB/B,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE0B,YAAY,CAAC;QACnEzE,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3BmF,uBAAuB,CAAC,MAAM,EAAEN,cAAc,CAAC;QAC/C;QACAO,UAAU,CAAC,MAAM;UACf1F,eAAe,CAAC2F,IAAI,IAAI;YACtB,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACF,IAAI,CAAC;YAC3CC,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;YAC3B,OAAOF,SAAS;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLxC,OAAO,CAAC2C,IAAI,CAAC,yBAAyB,EAAEhB,YAAY,CAAC;QACrD3B,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;QACtCpC,YAAY,CAAC+E,OAAO,CAACC,IAAI,IAAI;UAC3B,MAAMtC,OAAO,GAAGtC,sBAAsB,CAAC4E,IAAI,CAACZ,eAAe,CAAC,IAAI,EAAE;UAClE1B,OAAO,CAACqC,OAAO,CAACE,GAAG,IAAI;YACrB9C,OAAO,CAACC,GAAG,CAAC,OAAO6C,GAAG,CAACV,cAAc,KAAKS,IAAI,CAAClE,iBAAiB,GAAG,CAAC;UACtE,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA;QACA,IAAI,CAAC9B,OAAO,IAAI,CAACkB,mBAAmB,EAAE;UACpCiC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UACpDqC,UAAU,CAAC,MAAM;YACfrB,gBAAgB,CAAC,CAAC;UACpB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC,MAAM,IAAIU,YAAY,EAAE;MACvB3B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAI,CAACpD,OAAO,IAAI,CAACkB,mBAAmB,IAAIF,YAAY,CAAC4C,MAAM,KAAK,CAAC,EAAE;QACjET,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvDgB,gBAAgB,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAACtE,YAAY,EAAEkB,YAAY,EAAEI,sBAAsB,EAAEpB,OAAO,EAAEkB,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAExF,MAAMoD,0BAA0B,GAAG,MAAO4B,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MACrB,KAAK,MAAMxD,YAAY,IAAIuD,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAMhC,QAAQ,GAAG,MAAMhF,cAAc,CAACkH,wBAAwB,CAACzG,UAAU,EAAEgD,YAAY,CAACb,iBAAiB,CAAC;UAC1G;UACA,IAAI4B,OAAO,GAAG,EAAE;UAChB,IAAIQ,QAAQ,IAAIX,KAAK,CAACC,OAAO,CAACU,QAAQ,CAAC,EAAE;YACvCR,OAAO,GAAGQ,QAAQ;UACpB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAACR,OAAO,IAAIH,KAAK,CAACC,OAAO,CAACU,QAAQ,CAACR,OAAO,CAAC,EAAE;YAC1EA,OAAO,GAAGQ,QAAQ,CAACR,OAAO;UAC5B,CAAC,MAAM,IAAIQ,QAAQ,IAAIA,QAAQ,CAACP,IAAI,IAAIJ,KAAK,CAACC,OAAO,CAACU,QAAQ,CAACP,IAAI,CAAC,EAAE;YACpED,OAAO,GAAGQ,QAAQ,CAACP,IAAI;UACzB;UACAwC,UAAU,CAACxD,YAAY,CAACyC,eAAe,CAAC,GAAG1B,OAAO;QACpD,CAAC,CAAC,OAAOG,GAAG,EAAE;UACZV,OAAO,CAACjD,KAAK,CAAC,sCAAsCyC,YAAY,CAACb,iBAAiB,GAAG,EAAE+B,GAAG,CAAC;UAC3FsC,UAAU,CAACxD,YAAY,CAACyC,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MACA/D,yBAAyB,CAAC8E,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOtC,GAAG,EAAE;MACZV,OAAO,CAACjD,KAAK,CAAC,uCAAuC,EAAE2D,GAAG,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMwC,4BAA4B,GAAGA,CAACC,IAAI,EAAE3D,YAAY,GAAG,IAAI,KAAK;IAClElB,yBAAyB,CAAC6E,IAAI,CAAC;IAC/B3E,uBAAuB,CAACgB,YAAY,CAAC;IAErC,IAAI2D,IAAI,KAAK,MAAM,IAAI3D,YAAY,EAAE;MACnCd,uBAAuB,CAAC;QACtBC,iBAAiB,EAAEa,YAAY,CAACb,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEY,YAAY,CAACZ,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAEW,YAAY,CAACX,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMgF,6BAA6B,GAAGA,CAAA,KAAM;IAC1ChF,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BxB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMqG,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFrG,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACyB,oBAAoB,CAACE,iBAAiB,CAAC2E,IAAI,CAAC,CAAC,EAAE;QAClDtG,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACyB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjE7B,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIqB,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAMrC,mBAAmB,CAACuH,kBAAkB,CAAC/G,UAAU,EAAEiC,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAMrC,mBAAmB,CAACwH,kBAAkB,CAACjF,oBAAoB,CAAC0D,eAAe,EAAExD,oBAAoB,CAAC;MAC1G;MAEA2E,6BAA6B,CAAC,CAAC;MAC/B,MAAMnC,gBAAgB,CAAC,CAAC;MACxB,MAAMlB,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZV,OAAO,CAACjD,KAAK,CAAC,yBAAyB,EAAE2D,GAAG,CAAC;MAC7C1D,QAAQ,CAAC0D,GAAG,CAACa,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMkC,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAM5H,mBAAmB,CAAC6H,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAMzC,gBAAgB,CAAC,CAAC;MACxB,MAAMlB,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZV,OAAO,CAACjD,KAAK,CAAC,4BAA4B,EAAE2D,GAAG,CAAC;MAChD1D,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMqF,uBAAuB,GAAGA,CAACc,IAAI,EAAEW,OAAO,GAAG,IAAI,KAAK;IACxD7E,oBAAoB,CAACkE,IAAI,CAAC;IAC1BhE,kBAAkB,CAAC2E,OAAO,CAAC;IAE3B,IAAIX,IAAI,KAAK,MAAM,IAAIW,OAAO,EAAE;MAC9BzE,kBAAkB,CAAC;QACjBC,YAAY,EAAEwE,OAAO,CAACxE,YAAY;QAClCC,WAAW,EAAEuE,OAAO,CAACvE,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAEsE,OAAO,CAACtE,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAEqE,OAAO,CAACrE,aAAa,IAAI,EAAE;QAC1CC,kBAAkB,EAAEoE,OAAO,CAACpE,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAX,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMgF,wBAAwB,GAAGA,CAAA,KAAM;IACrChF,oBAAoB,CAAC,KAAK,CAAC;IAC3BI,kBAAkB,CAAC,IAAI,CAAC;IACxBE,kBAAkB,CAAC;MACjBC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsE,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,IAAIhF,iBAAiB,KAAK,MAAM,EAAE;QAChC,MAAMjD,cAAc,CAACkI,aAAa,CAAC/E,eAAe,CAACkD,cAAc,EAAEhD,eAAe,CAAC;QACnF2E,wBAAwB,CAAC,CAAC;QAC1B,MAAM9C,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAMlB,WAAW,CAAC,CAAC;QACnB,MAAMY,eAAe,CAAC,CAAC;MACzB;IACF,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZV,OAAO,CAACjD,KAAK,CAAC,yBAAyB,EAAE2D,GAAG,CAAC;MAC7C1D,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAMkH,mBAAmB,GAAG,MAAOC,aAAa,IAAK;IACnD,IAAI,CAACR,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAM7H,cAAc,CAACqI,aAAa,CAACD,aAAa,CAAC;MACjD,MAAMlD,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC1B,MAAMlB,WAAW,CAAC,CAAC;MACnB,MAAMY,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZV,OAAO,CAACjD,KAAK,CAAC,4BAA4B,EAAE2D,GAAG,CAAC;MAChD1D,QAAQ,CAAC,yCAAyC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMqH,0BAA0B,GAAIP,OAAO,IAAK;IAC9ChE,kBAAkB,CAACgE,OAAO,CAAC;IAC3BlE,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAM0E,2BAA2B,GAAGA,CAAA,KAAM;IACxC1E,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMyE,6BAA6B,GAAG,MAAOvD,OAAO,IAAK;IACvDhB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEe,OAAO,CAAC;IACrDhE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMyE,OAAO,CAACC,GAAG,CAAC,CAChBT,gBAAgB,CAAC,CAAC,EAClBlB,WAAW,CAAC,CAAC,EACbY,eAAe,CAAC,CAAC,CAClB,CAAC;;IAEF;IACA;EACF,CAAC;EAED,MAAM6D,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE,gBAAgB;MAClC,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAID,IAAI/H,OAAO,IAAIU,cAAc,EAAE;IAC7B,oBACEjB,OAAA,CAACvC,GAAG;MAAC+K,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/E5I,OAAA,CAAC5B,gBAAgB;QAAAyK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEhJ,OAAA,CAACvC,GAAG;IAACwL,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAEhB5I,OAAA,CAACvC,GAAG;MAAC0L,EAAE,EAAE,CAAE;MAAAP,QAAA,eACT5I,OAAA,CAACtC,UAAU;QAAC0L,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAV,QAAA,EACrEzI;MAAY;QAAA0I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELvI,KAAK,iBACJT,OAAA,CAAC7B,KAAK;MAACoL,QAAQ,EAAC,OAAO;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnCnI;IAAK;MAAAoI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEArI,gBAAgB,iBACfX,OAAA,CAAC7B,KAAK;MAACoL,QAAQ,EAAC,MAAM;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,GAAC,+BACjB,EAACjI,gBAAgB,EAAC,cACvC;IAAA;MAAAkI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAGAnI,WAAW,iBACVb,OAAA,CAACpC,KAAK;MAACqL,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEK,OAAO,EAAE;MAAU,CAAE;MAAAZ,QAAA,eAC7C5I,OAAA,CAACxB,KAAK;QAACiL,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAChB,UAAU,EAAC,QAAQ;QAACD,cAAc,EAAC,eAAe;QAACkB,QAAQ,EAAC,MAAM;QAAAf,QAAA,gBAEnG5I,OAAA,CAACxB,KAAK;UAACiL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD5I,OAAA,CAAChB,UAAU;YAACsK,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/ChJ,OAAA,CAACvC,GAAG;YAAAmL,QAAA,gBACF5I,OAAA,CAACtC,UAAU;cAAC0L,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D/H,WAAW,CAACiJ,mBAAmB,IAAI;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACbhJ,OAAA,CAACtC,UAAU;cAAC0L,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRhJ,OAAA,CAACxB,KAAK;UAACiL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD5I,OAAA,CAAClB,UAAU;YAACwK,KAAK,EAAC,MAAM;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5ChJ,OAAA,CAACvC,GAAG;YAAAmL,QAAA,gBACF5I,OAAA,CAACtC,UAAU;cAAC0L,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D/H,WAAW,CAACkJ,cAAc,IAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACbhJ,OAAA,CAACtC,UAAU;cAAC0L,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRhJ,OAAA,CAACxB,KAAK;UAACiL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD5I,OAAA,CAACd,eAAe;YAACoK,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDhJ,OAAA,CAACvC,GAAG;YAAAmL,QAAA,gBACF5I,OAAA,CAACtC,UAAU;cAAC0L,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D/H,WAAW,CAACmJ,gBAAgB,IAAI;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACbhJ,OAAA,CAACtC,UAAU;cAAC0L,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRhJ,OAAA,CAACxB,KAAK;UAACiL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD5I,OAAA,CAACZ,YAAY;YAACkK,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDhJ,OAAA,CAACvC,GAAG;YAAAmL,QAAA,gBACF5I,OAAA,CAACtC,UAAU;cAAC0L,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D/H,WAAW,CAACoJ,kBAAkB,IAAI;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACbhJ,OAAA,CAACtC,UAAU;cAAC0L,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRhJ,OAAA,CAACxB,KAAK;UAACiL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD5I,OAAA,CAACvC,GAAG;YAACwL,EAAE,EAAE;cACPiB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,YAAY,EAAE,KAAK;cACnBZ,OAAO,EAAG3I,WAAW,CAACoJ,kBAAkB,IAAIpJ,WAAW,CAACkJ,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAC3FlJ,WAAW,CAACoJ,kBAAkB,IAAIpJ,WAAW,CAACkJ,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAAG,YAAY;cACpHvB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACA5I,OAAA,CAACtC,UAAU;cAAC0L,OAAO,EAAC,SAAS;cAACC,UAAU,EAAC,MAAM;cAACC,KAAK,EAAC,OAAO;cAAAV,QAAA,GAC1D/H,WAAW,CAACkJ,cAAc,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAEzJ,WAAW,CAACoJ,kBAAkB,GAAGpJ,WAAW,CAACkJ,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GACxH;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNhJ,OAAA,CAACvC,GAAG;YAAAmL,QAAA,gBACF5I,OAAA,CAACtC,UAAU;cAAC0L,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,QAAQ;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhJ,OAAA,CAACtC,UAAU;cAAC0L,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,GACjD/H,WAAW,CAAC0J,cAAc,IAAI,CAAC,EAAC,SACnC;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAGDhJ,OAAA,CAACvC,GAAG;MAAAmL,QAAA,eACF5I,OAAA,CAACvC,GAAG;QAAAmL,QAAA,gBAEF5I,OAAA,CAACvC,GAAG;UAAC+K,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACS,EAAE,EAAE,CAAE;UAAAP,QAAA,gBAC3E5I,OAAA,CAACtC,UAAU;YAAC0L,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAe,CAAE;YAAAV,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhJ,OAAA,CAACvC,GAAG;YAAC+K,OAAO,EAAC,MAAM;YAACgC,GAAG,EAAE,CAAE;YAAA5B,QAAA,gBACzB5I,OAAA,CAACrC,MAAM;cACLyL,OAAO,EAAC,UAAU;cAClBqB,SAAS,eAAEzK,OAAA,CAACV,UAAU;gBAAAuJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1B0B,OAAO,EAAEA,CAAA,KAAMtJ,wBAAwB,CAAC,IAAI,CAAE;cAC9C6H,EAAE,EAAE;gBACF0B,aAAa,EAAE,MAAM;gBACrBtB,UAAU,EAAE,GAAG;gBACfuB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BxB,KAAK,EAAE,SAAS;gBAChByB,MAAM,EAAE,mBAAmB;gBAC3B,SAAS,EAAE;kBACTD,eAAe,EAAE,yBAAyB;kBAC1CE,WAAW,EAAE;gBACf;cACF,CAAE;cAAApC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThJ,OAAA,CAACrC,MAAM;cACLyL,OAAO,EAAC,WAAW;cACnBqB,SAAS,eAAEzK,OAAA,CAACpB,OAAO;gBAAAiK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvB0B,OAAO,EAAEA,CAAA,KAAM9D,4BAA4B,CAAC,QAAQ,CAAE;cACtDqC,EAAE,EAAE;gBACF0B,aAAa,EAAE,MAAM;gBACrBtB,UAAU,EAAE,GAAG;gBACfuB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BxB,KAAK,EAAE,OAAO;gBACd,SAAS,EAAE;kBACTwB,eAAe,EAAE;gBACnB;cACF,CAAE;cAAAlC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL/H,cAAc,gBACbjB,OAAA,CAACvC,GAAG;UAAC+K,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAACoC,EAAE,EAAE,CAAE;UAAAjC,QAAA,eAChD5I,OAAA,CAAC5B,gBAAgB;YAAAyK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJjI,UAAU,CAACoD,MAAM,KAAK,CAAC,gBACzBnE,OAAA,CAACpC,KAAK;UACJqN,SAAS,EAAE,CAAE;UACbhC,EAAE,EAAE;YACFC,CAAC,EAAE,CAAC;YACJgC,SAAS,EAAE,QAAQ;YACnBJ,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,YAAY;YACpBC,WAAW,EAAE;UACf,CAAE;UAAApC,QAAA,gBAEF5I,OAAA,CAAClB,UAAU;YAACmK,EAAE,EAAE;cAAEW,QAAQ,EAAE,EAAE;cAAEN,KAAK,EAAE,UAAU;cAAEH,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DhJ,OAAA,CAACtC,UAAU;YAAC0L,OAAO,EAAC,IAAI;YAACE,KAAK,EAAC,gBAAgB;YAAC6B,YAAY;YAAAvC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhJ,OAAA,CAACtC,UAAU;YAAC0L,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAACL,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhJ,OAAA,CAACrC,MAAM;YACLyL,OAAO,EAAC,WAAW;YACnBqB,SAAS,eAAEzK,OAAA,CAACpB,OAAO;cAAAiK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB0B,OAAO,EAAEA,CAAA,KAAMpJ,kBAAkB,CAAC,IAAI,CAAE;YACxC2H,EAAE,EAAE;cACF0B,aAAa,EAAE,MAAM;cACrBG,eAAe,EAAE,SAAS;cAC1BxB,KAAK,EAAE,OAAO;cACd,SAAS,EAAE;gBACTwB,eAAe,EAAE;cACnB;YACF,CAAE;YAAAlC,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAERhJ,OAAA,CAACH,gBAAgB;UACfoE,OAAO,EAAElD,UAAW;UACpBqK,aAAa,EAAErF,uBAAuB,CAACsF,IAAI,CAAC,IAAI,EAAE,MAAM,CAAE;UAC1DC,eAAe,EAAE1D,mBAAoB;UACrC2D,kBAAkB,EAAExD,0BAA2B;UAC/CxH,OAAO,EAAEU;QAAe;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhJ,OAAA,CAAClC,MAAM;MACL0N,IAAI,EAAE3J,sBAAuB;MAC7B4J,OAAO,EAAE3E,6BAA8B;MACvC4E,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV3C,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEF5I,OAAA,CAACjC,WAAW;QAACkL,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAjD,QAAA,eACzB5I,OAAA,CAACtC,UAAU;UAAC0L,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9C7G,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdhJ,OAAA,CAAChC,aAAa;QAAA4K,QAAA,eACZ5I,OAAA,CAACvC,GAAG;UAACwL,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACjB5I,OAAA,CAAC9B,SAAS;YACRyN,SAAS;YACTI,KAAK,EAAC,mBAAmB;YACzBC,KAAK,EAAE7J,oBAAoB,CAACE,iBAAkB;YAC9C4J,QAAQ,EAAGC,CAAC,IAAK9J,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAE6J,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzGI,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRjD,OAAO,EAAC,UAAU;YAClBH,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFhJ,OAAA,CAAC9B,SAAS;YACRyN,SAAS;YACTI,KAAK,EAAC,OAAO;YACbO,IAAI,EAAC,OAAO;YACZN,KAAK,EAAE7J,oBAAoB,CAACG,KAAM;YAClC2J,QAAQ,EAAGC,CAAC,IAAK9J,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAE4J,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC7FI,MAAM,EAAC,QAAQ;YACfhD,OAAO,EAAC,UAAU;YAClBmD,UAAU,EAAC,uDAAuD;YAClEtD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFhJ,OAAA,CAAC9B,SAAS;YACRyN,SAAS;YACTI,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAE7J,oBAAoB,CAACI,QAAS;YACrC0J,QAAQ,EAAGC,CAAC,IAAK9J,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAE2J,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAChGI,MAAM,EAAC,QAAQ;YACfhD,OAAO,EAAC,UAAU;YAClBmD,UAAU,EAAC;UAA+C;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBhJ,OAAA,CAAC/B,aAAa;QAACgL,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAlD,QAAA,gBACjC5I,OAAA,CAACrC,MAAM;UACL+M,OAAO,EAAE5D,6BAA8B;UACvCmC,EAAE,EAAE;YAAE0B,aAAa,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThJ,OAAA,CAACrC,MAAM;UACL+M,OAAO,EAAE3D,wBAAyB;UAClCqC,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACF0B,aAAa,EAAE,MAAM;YACrBtB,UAAU,EAAE,GAAG;YACfuB,EAAE,EAAE;UACN,CAAE;UAAAhC,QAAA,EAED7G,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGThJ,OAAA,CAAClC,MAAM;MACL0N,IAAI,EAAEhJ,iBAAkB;MACxBiJ,OAAO,EAAEhE,wBAAyB;MAClCiE,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV3C,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEF5I,OAAA,CAACjC,WAAW;QAACkL,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAjD,QAAA,eACzB5I,OAAA,CAACtC,UAAU;UAAC0L,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdhJ,OAAA,CAAChC,aAAa;QAAA4K,QAAA,eACZ5I,OAAA,CAACvC,GAAG;UAACwL,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACjB5I,OAAA,CAAC9B,SAAS;YACRyN,SAAS;YACTa,MAAM;YACNT,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAElJ,eAAe,CAACE,YAAa;YACpCiJ,QAAQ,EAAGC,CAAC,IAAKnJ,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEE,YAAY,EAAEkJ,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC1FI,MAAM,EAAC,QAAQ;YACfnD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBAEd5I,OAAA,CAACvB,QAAQ;cAACuN,KAAK,EAAC,MAAM;cAAApD,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtChJ,OAAA,CAACvB,QAAQ;cAACuN,KAAK,EAAC,uBAAuB;cAAApD,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxEhJ,OAAA,CAACvB,QAAQ;cAACuN,KAAK,EAAC,qBAAqB;cAAApD,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpEhJ,OAAA,CAACvB,QAAQ;cAACuN,KAAK,EAAC,gBAAgB;cAAApD,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1DhJ,OAAA,CAACvB,QAAQ;cAACuN,KAAK,EAAC,SAAS;cAAApD,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEZhJ,OAAA,CAAC9B,SAAS;YACRyN,SAAS;YACTI,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAElJ,eAAe,CAACG,WAAY;YACnCgJ,QAAQ,EAAGC,CAAC,IAAKnJ,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEG,WAAW,EAAEiJ,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzFI,MAAM,EAAC,QAAQ;YACfK,SAAS;YACTC,IAAI,EAAE,CAAE;YACRzD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFhJ,OAAA,CAAC9B,SAAS;YACRyN,SAAS;YACTI,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAElJ,eAAe,CAACI,YAAa;YACpC+I,QAAQ,EAAGC,CAAC,IAAKnJ,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEI,YAAY,EAAEgJ,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC1FI,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRE,UAAU,EAAC,0CAAuC;YAClDtD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFhJ,OAAA,CAAC9B,SAAS;YACRyN,SAAS;YACTI,KAAK,EAAC,oBAAoB;YAC1BC,KAAK,EAAElJ,eAAe,CAACM,kBAAmB;YAC1C6I,QAAQ,EAAGC,CAAC,IAAKnJ,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEM,kBAAkB,EAAE8I,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAChGI,MAAM,EAAC,QAAQ;YACfK,SAAS;YACTC,IAAI,EAAE,CAAE;YACRH,UAAU,EAAC,2CAA2C;YACtDtD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFhJ,OAAA,CAAC9B,SAAS;YACRyN,SAAS;YACTI,KAAK,EAAC,eAAe;YACrBO,IAAI,EAAC,MAAM;YACXN,KAAK,EAAElJ,eAAe,CAACK,aAAc;YACrC8I,QAAQ,EAAGC,CAAC,IAAKnJ,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEK,aAAa,EAAE+I,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC3FI,MAAM,EAAC,QAAQ;YACfO,eAAe,EAAE;cACfC,MAAM,EAAE;YACV;UAAE;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBhJ,OAAA,CAAC/B,aAAa;QAACgL,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAlD,QAAA,gBACjC5I,OAAA,CAACrC,MAAM;UACL+M,OAAO,EAAEjD,wBAAyB;UAClCwB,EAAE,EAAE;YAAE0B,aAAa,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThJ,OAAA,CAACrC,MAAM;UACL+M,OAAO,EAAEhD,mBAAoB;UAC7B0B,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACF0B,aAAa,EAAE,MAAM;YACrBtB,UAAU,EAAE,GAAG;YACfuB,EAAE,EAAE;UACN,CAAE;UAAAhC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGThJ,OAAA,CAACL,kBAAkB;MACjBO,UAAU,EAAEA,UAAW;MACvBsL,IAAI,EAAEnK,eAAgB;MACtBoK,OAAO,EAAEA,CAAA,KAAMnK,kBAAkB,CAAC,KAAK,CAAE;MACzCuL,SAAS,EAAEA,CAACpI,QAAQ,EAAEqI,cAAc,KAAK;QACvCpJ,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;;QAE9D;QACA,IAAImJ,cAAc,EAAE;UAClB;UACApJ,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEmJ,cAAc,CAAC;QAC7C;;QAEA;QACArJ,WAAW,CAAC,CAAC;QACbY,eAAe,CAAC,CAAC;QACjBM,gBAAgB,CAAC,CAAC;QAClBrD,kBAAkB,CAAC,KAAK,CAAC;QAEzBoC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACzC;IAAE;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFhJ,OAAA,CAACJ,qBAAqB;MACpB4L,IAAI,EAAErK,qBAAsB;MAC5BsK,OAAO,EAAEA,CAAA,KAAMrK,wBAAwB,CAAC,KAAK,CAAE;MAC/CG,YAAY,EAAEA,YAAa;MAC3BI,sBAAsB,EAAEA,sBAAuB;MAC/CoL,kBAAkB,EAAG7J,YAAY,IAAK;QACpC9B,wBAAwB,CAAC,KAAK,CAAC;QAC/BwF,4BAA4B,CAAC,MAAM,EAAE1D,YAAY,CAAC;MACpD,CAAE;MACF8J,oBAAoB,EAAE,MAAO5F,cAAc,IAAK;QAC9C,MAAMD,wBAAwB,CAACC,cAAc,CAAC;QAC9ChG,wBAAwB,CAAC,KAAK,CAAC;MACjC,CAAE;MACFb,OAAO,EAAEkB,mBAAoB;MAC7BhB,KAAK,EAAEA;IAAM;MAAAoI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGFhJ,OAAA,CAACF,sBAAsB;MACrB0L,IAAI,EAAEnI,oBAAqB;MAC3BoI,OAAO,EAAEzD,2BAA4B;MACrCR,OAAO,EAAEjE,eAAgB;MACzBsJ,SAAS,EAAE5E;IAA8B;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC5I,EAAA,CAn1BIH,wBAAwB;EAAA,QAEYzC,eAAe;AAAA;AAAAyP,EAAA,GAFnDhN,wBAAwB;AAq1B9B,eAAeA,wBAAwB;AAAC,IAAAgN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}