[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "16", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\config.js": "19", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "20", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "21", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "29", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "30", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "31", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "32", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "33", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "34", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "35", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "36", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "37", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "42", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "43", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "44", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "47", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "49", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "51", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "52", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "56", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "58", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "59", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "60", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "61", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "62", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "63", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "64", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "65", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "70", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "71", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "72", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "73", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "74", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "75", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "76", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "81", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js": "87", "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js": "88", "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js": "89", "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js": "90", "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js": "91", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js": "92", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js": "93", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js": "94", "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js": "95", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js": "96", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js": "97", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialogCompleto.js": "98", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialogCompleto.js": "99", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CreateCantiereDialog.js": "100", "C:\\CMS\\webapp\\frontend\\src\\services\\weatherService.js": "101", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaMultipla.js": "102", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ValidationResultsDialog.js": "103", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeValidationService.js": "104", "C:\\CMS\\webapp\\frontend\\src\\services\\responsabiliService.js": "105", "C:\\CMS\\webapp\\frontend\\src\\components\\responsabili\\GestioneResponsabili.js": "106", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListRivoluzionato.js": "107", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ResponsabiliListPopup.js": "108", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListTable.js": "109", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\TipologieCaviManager.js": "110", "C:\\CMS\\webapp\\frontend\\src\\services\\tipologieCaviService.js": "111", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\InserimentoMetriDialog.js": "112"}, {"size": 557, "mtime": 1746952718482, "results": "113", "hashOfConfig": "114"}, {"size": 3196, "mtime": 1748982170834, "results": "115", "hashOfConfig": "114"}, {"size": 996, "mtime": 1746970152489, "results": "116", "hashOfConfig": "114"}, {"size": 11085, "mtime": 1749414725518, "results": "117", "hashOfConfig": "114"}, {"size": 21191, "mtime": 1748751093271, "results": "118", "hashOfConfig": "114"}, {"size": 4987, "mtime": 1749534568885, "results": "119", "hashOfConfig": "114"}, {"size": 2216, "mtime": 1746640055487, "results": "120", "hashOfConfig": "114"}, {"size": 7394, "mtime": 1748034003517, "results": "121", "hashOfConfig": "114"}, {"size": 7347, "mtime": 1749615166316, "results": "122", "hashOfConfig": "114"}, {"size": 15637, "mtime": 1749188445361, "results": "123", "hashOfConfig": "114"}, {"size": 2455, "mtime": 1749188313610, "results": "124", "hashOfConfig": "114"}, {"size": 2050, "mtime": 1746647945415, "results": "125", "hashOfConfig": "114"}, {"size": 700, "mtime": 1747545501078, "results": "126", "hashOfConfig": "114"}, {"size": 14255, "mtime": 1749366744543, "results": "127", "hashOfConfig": "114"}, {"size": 3028, "mtime": 1748816305304, "results": "128", "hashOfConfig": "114"}, {"size": 1630, "mtime": 1746336079554, "results": "129", "hashOfConfig": "114"}, {"size": 1909, "mtime": 1748722592098, "results": "130", "hashOfConfig": "114"}, {"size": 63160, "mtime": 1749534525890, "results": "131", "hashOfConfig": "114"}, {"size": 324, "mtime": 1749501001043, "results": "132", "hashOfConfig": "114"}, {"size": 9068, "mtime": 1746856425683, "results": "133", "hashOfConfig": "114"}, {"size": 2210, "mtime": 1747432283057, "results": "134", "hashOfConfig": "114"}, {"size": 4494, "mtime": 1748121063631, "results": "135", "hashOfConfig": "114"}, {"size": 52535, "mtime": 1749414973931, "results": "136", "hashOfConfig": "114"}, {"size": 3337, "mtime": 1748816346924, "results": "137", "hashOfConfig": "114"}, {"size": 2958, "mtime": 1748816316425, "results": "138", "hashOfConfig": "114"}, {"size": 3507, "mtime": 1748816326922, "results": "139", "hashOfConfig": "114"}, {"size": 3340, "mtime": 1748816336281, "results": "140", "hashOfConfig": "114"}, {"size": 6097, "mtime": 1749534549483, "results": "141", "hashOfConfig": "114"}, {"size": 5880, "mtime": 1748121404574, "results": "142", "hashOfConfig": "114"}, {"size": 3889, "mtime": 1748664890350, "results": "143", "hashOfConfig": "114"}, {"size": 4720, "mtime": 1746771178920, "results": "144", "hashOfConfig": "114"}, {"size": 7681, "mtime": 1749184406942, "results": "145", "hashOfConfig": "114"}, {"size": 10819, "mtime": 1749184481438, "results": "146", "hashOfConfig": "114"}, {"size": 6259, "mtime": 1746965906057, "results": "147", "hashOfConfig": "114"}, {"size": 4215, "mtime": 1746278746358, "results": "148", "hashOfConfig": "114"}, {"size": 1273, "mtime": 1746809069006, "results": "149", "hashOfConfig": "114"}, {"size": 14270, "mtime": 1748371983481, "results": "150", "hashOfConfig": "114"}, {"size": 2752, "mtime": 1747022186740, "results": "151", "hashOfConfig": "114"}, {"size": 1072, "mtime": 1746637929350, "results": "152", "hashOfConfig": "114"}, {"size": 6745, "mtime": 1747545492454, "results": "153", "hashOfConfig": "114"}, {"size": 539, "mtime": 1749491416504, "results": "154", "hashOfConfig": "114"}, {"size": 43883, "mtime": 1749161040576, "results": "155", "hashOfConfig": "114"}, {"size": 1947, "mtime": 1748120984640, "results": "156", "hashOfConfig": "114"}, {"size": 53290, "mtime": 1749500344230, "results": "157", "hashOfConfig": "114"}, {"size": 13911, "mtime": 1749069212408, "results": "158", "hashOfConfig": "114"}, {"size": 19869, "mtime": 1749531889837, "results": "159", "hashOfConfig": "114"}, {"size": 11835, "mtime": 1748920731807, "results": "160", "hashOfConfig": "114"}, {"size": 2211, "mtime": 1748686293878, "results": "161", "hashOfConfig": "114"}, {"size": 9215, "mtime": 1749162481509, "results": "162", "hashOfConfig": "114"}, {"size": 10993, "mtime": 1747154871546, "results": "163", "hashOfConfig": "114"}, {"size": 12217, "mtime": 1749161883257, "results": "164", "hashOfConfig": "114"}, {"size": 20081, "mtime": 1749162690470, "results": "165", "hashOfConfig": "114"}, {"size": 7032, "mtime": 1748069273238, "results": "166", "hashOfConfig": "114"}, {"size": 8589, "mtime": 1748207111023, "results": "167", "hashOfConfig": "114"}, {"size": 13653, "mtime": 1749367215461, "results": "168", "hashOfConfig": "114"}, {"size": 12817, "mtime": 1749183241975, "results": "169", "hashOfConfig": "114"}, {"size": 36555, "mtime": 1747684003188, "results": "170", "hashOfConfig": "114"}, {"size": 9128, "mtime": 1749069292534, "results": "171", "hashOfConfig": "114"}, {"size": 20387, "mtime": 1748984521895, "results": "172", "hashOfConfig": "114"}, {"size": 522, "mtime": 1747022186711, "results": "173", "hashOfConfig": "114"}, {"size": 11907, "mtime": 1749189769410, "results": "174", "hashOfConfig": "114"}, {"size": 10637, "mtime": 1749675037900, "results": "175", "hashOfConfig": "114"}, {"size": 1703, "mtime": 1746972529152, "results": "176", "hashOfConfig": "114"}, {"size": 18402, "mtime": 1749156991134, "results": "177", "hashOfConfig": "114"}, {"size": 12050, "mtime": 1747547543421, "results": "178", "hashOfConfig": "114"}, {"size": 1686, "mtime": 1746946499500, "results": "179", "hashOfConfig": "114"}, {"size": 5145, "mtime": 1746914029633, "results": "180", "hashOfConfig": "114"}, {"size": 10253, "mtime": 1749156772006, "results": "181", "hashOfConfig": "114"}, {"size": 32582, "mtime": 1749161413266, "results": "182", "hashOfConfig": "114"}, {"size": 2574, "mtime": 1748920719208, "results": "183", "hashOfConfig": "114"}, {"size": 4094, "mtime": 1748161663641, "results": "184", "hashOfConfig": "114"}, {"size": 4717, "mtime": 1749142942884, "results": "185", "hashOfConfig": "114"}, {"size": 4346, "mtime": 1747491472989, "results": "186", "hashOfConfig": "114"}, {"size": 15647, "mtime": 1748899398456, "results": "187", "hashOfConfig": "114"}, {"size": 7659, "mtime": 1749366714525, "results": "188", "hashOfConfig": "114"}, {"size": 12341, "mtime": 1749366595552, "results": "189", "hashOfConfig": "114"}, {"size": 15764, "mtime": 1748877145346, "results": "190", "hashOfConfig": "114"}, {"size": 6899, "mtime": 1748877131332, "results": "191", "hashOfConfig": "114"}, {"size": 5536, "mtime": 1748670096009, "results": "192", "hashOfConfig": "114"}, {"size": 5457, "mtime": 1748666884369, "results": "193", "hashOfConfig": "114"}, {"size": 5605, "mtime": 1748666925194, "results": "194", "hashOfConfig": "114"}, {"size": 82038, "mtime": 1749413441723, "results": "195", "hashOfConfig": "114"}, {"size": 22794, "mtime": 1749490955320, "results": "196", "hashOfConfig": "114"}, {"size": 3708, "mtime": 1748705727900, "results": "197", "hashOfConfig": "114"}, {"size": 10270, "mtime": 1748724524628, "results": "198", "hashOfConfig": "114"}, {"size": 15055, "mtime": 1748755908778, "results": "199", "hashOfConfig": "114"}, {"size": 16415, "mtime": 1748755956687, "results": "200", "hashOfConfig": "114"}, {"size": 3434, "mtime": 1748755857115, "results": "201", "hashOfConfig": "114"}, {"size": 3483, "mtime": 1748755829302, "results": "202", "hashOfConfig": "114"}, {"size": 3508, "mtime": 1748755842942, "results": "203", "hashOfConfig": "114"}, {"size": 956, "mtime": 1748878396989, "results": "204", "hashOfConfig": "114"}, {"size": 18844, "mtime": 1749583605637, "results": "205", "hashOfConfig": "114"}, {"size": 16151, "mtime": 1748981113532, "results": "206", "hashOfConfig": "114"}, {"size": 3613, "mtime": 1748921268108, "results": "207", "hashOfConfig": "114"}, {"size": 1153, "mtime": 1748921279608, "results": "208", "hashOfConfig": "114"}, {"size": 6579, "mtime": 1748922219011, "results": "209", "hashOfConfig": "114"}, {"size": 8976, "mtime": 1748922249445, "results": "210", "hashOfConfig": "114"}, {"size": 29408, "mtime": 1749154958292, "results": "211", "hashOfConfig": "114"}, {"size": 24586, "mtime": 1749155015426, "results": "212", "hashOfConfig": "114"}, {"size": 11668, "mtime": 1749366480246, "results": "213", "hashOfConfig": "114"}, {"size": 3389, "mtime": 1749417624087, "results": "214", "hashOfConfig": "114"}, {"size": 15031, "mtime": 1749439918292, "results": "215", "hashOfConfig": "114"}, {"size": 15003, "mtime": 1749419448637, "results": "216", "hashOfConfig": "114"}, {"size": 14243, "mtime": 1749419412675, "results": "217", "hashOfConfig": "114"}, {"size": 4833, "mtime": 1749487592760, "results": "218", "hashOfConfig": "114"}, {"size": 14380, "mtime": 1749489582520, "results": "219", "hashOfConfig": "114"}, {"size": 30059, "mtime": 1749675056244, "results": "220", "hashOfConfig": "114"}, {"size": 7486, "mtime": 1749556154284, "results": "221", "hashOfConfig": "114"}, {"size": 10601, "mtime": 1749673950201, "results": "222", "hashOfConfig": "114"}, {"size": 41221, "mtime": 1749620791154, "results": "223", "hashOfConfig": "114"}, {"size": 10103, "mtime": 1749620669254, "results": "224", "hashOfConfig": "114"}, {"size": 9266, "mtime": 1749673222695, "results": "225", "hashOfConfig": "114"}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", ["562"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["563", "564", "565", "566"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["579"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["580", "581", "582", "583", "584", "585", "586", "587", "588", "589"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", ["590"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["606"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["607", "608", "609", "610", "611", "612", "613", "614"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["647"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["648"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["649"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["650", "651", "652", "653"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", ["654", "655"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", ["656", "657"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["658", "659", "660", "661"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["662", "663"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["664", "665"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["666", "667"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["668", "669"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["711", "712", "713", "714"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", ["715", "716", "717", "718", "719", "720", "721", "722", "723"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["724", "725"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["737"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["760"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["761"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["762"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["774", "775", "776", "777"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["792", "793"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["794"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["795", "796", "797", "798", "799", "800"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["818", "819", "820", "821", "822"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["823"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["824", "825", "826"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["827", "828"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["829", "830"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", ["831"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["832"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["833"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js", ["834", "835", "836", "837", "838", "839"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js", ["840", "841"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js", ["842", "843", "844", "845", "846", "847", "848", "849", "850"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js", ["851", "852", "853", "854", "855", "856", "857", "858", "859"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js", ["860", "861", "862", "863", "864", "865"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js", ["866", "867", "868"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js", ["869", "870", "871", "872"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialogCompleto.js", ["873", "874", "875", "876"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialogCompleto.js", ["877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CreateCantiereDialog.js", ["901"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\weatherService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaMultipla.js", ["902", "903", "904", "905", "906"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ValidationResultsDialog.js", ["907"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeValidationService.js", ["908", "909", "910", "911", "912"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\responsabiliService.js", ["913", "914", "915"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\responsabili\\GestioneResponsabili.js", ["916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListRivoluzionato.js", ["927", "928", "929", "930", "931", "932", "933", "934"], ["935", "936"], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ResponsabiliListPopup.js", ["937", "938"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\TipologieCaviManager.js", ["939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\tipologieCaviService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\InserimentoMetriDialog.js", ["950", "951", "952", "953"], [], {"ruleId": "954", "severity": 1, "message": "955", "line": 12, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 12, "endColumn": 14}, {"ruleId": "958", "severity": 1, "message": "959", "line": 78, "column": 11, "nodeType": "960", "messageId": "961", "endLine": 78, "endColumn": 115}, {"ruleId": "958", "severity": 1, "message": "959", "line": 80, "column": 11, "nodeType": "960", "messageId": "961", "endLine": 80, "endColumn": 107}, {"ruleId": "958", "severity": 1, "message": "959", "line": 86, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 86, "endColumn": 105}, {"ruleId": "958", "severity": 1, "message": "959", "line": 89, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 89, "endColumn": 41}, {"ruleId": "954", "severity": 1, "message": "962", "line": 13, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 13, "endColumn": 9}, {"ruleId": "954", "severity": 1, "message": "963", "line": 20, "column": 25, "nodeType": "956", "messageId": "957", "endLine": 20, "endColumn": 34}, {"ruleId": "954", "severity": 1, "message": "964", "line": 21, "column": 19, "nodeType": "956", "messageId": "957", "endLine": 21, "endColumn": 35}, {"ruleId": "954", "severity": 1, "message": "965", "line": 22, "column": 12, "nodeType": "956", "messageId": "957", "endLine": 22, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "966", "line": 23, "column": 18, "nodeType": "956", "messageId": "957", "endLine": 23, "endColumn": 28}, {"ruleId": "954", "severity": 1, "message": "967", "line": 40, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 40, "endColumn": 35}, {"ruleId": "954", "severity": 1, "message": "968", "line": 40, "column": 37, "nodeType": "956", "messageId": "957", "endLine": 40, "endColumn": 62}, {"ruleId": "954", "severity": 1, "message": "969", "line": 57, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 57, "endColumn": 22}, {"ruleId": "954", "severity": 1, "message": "970", "line": 58, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 58, "endColumn": 23}, {"ruleId": "954", "severity": 1, "message": "971", "line": 59, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 59, "endColumn": 26}, {"ruleId": "954", "severity": 1, "message": "972", "line": 60, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 60, "endColumn": 22}, {"ruleId": "954", "severity": 1, "message": "973", "line": 69, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 69, "endColumn": 29}, {"ruleId": "954", "severity": 1, "message": "974", "line": 1, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "975", "line": 2, "column": 27, "nodeType": "956", "messageId": "957", "endLine": 2, "endColumn": 31}, {"ruleId": "954", "severity": 1, "message": "976", "line": 2, "column": 33, "nodeType": "956", "messageId": "957", "endLine": 2, "endColumn": 37}, {"ruleId": "954", "severity": 1, "message": "977", "line": 2, "column": 39, "nodeType": "956", "messageId": "957", "endLine": 2, "endColumn": 50}, {"ruleId": "954", "severity": 1, "message": "978", "line": 2, "column": 52, "nodeType": "956", "messageId": "957", "endLine": 2, "endColumn": 66}, {"ruleId": "954", "severity": 1, "message": "962", "line": 2, "column": 68, "nodeType": "956", "messageId": "957", "endLine": 2, "endColumn": 74}, {"ruleId": "954", "severity": 1, "message": "963", "line": 5, "column": 25, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 34}, {"ruleId": "954", "severity": 1, "message": "964", "line": 6, "column": 19, "nodeType": "956", "messageId": "957", "endLine": 6, "endColumn": 35}, {"ruleId": "954", "severity": 1, "message": "965", "line": 7, "column": 12, "nodeType": "956", "messageId": "957", "endLine": 7, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "966", "line": 8, "column": 18, "nodeType": "956", "messageId": "957", "endLine": 8, "endColumn": 28}, {"ruleId": "954", "severity": 1, "message": "979", "line": 43, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 43, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "980", "line": 16, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 16, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "976", "line": 8, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 8, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "977", "line": 9, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 9, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "955", "line": 10, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 10, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "975", "line": 11, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 11, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "981", "line": 12, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 12, "endColumn": 10}, {"ruleId": "954", "severity": 1, "message": "982", "line": 15, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 15, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "983", "line": 16, "column": 15, "nodeType": "956", "messageId": "957", "endLine": 16, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "984", "line": 17, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 17, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "985", "line": 18, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 18, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "986", "line": 19, "column": 13, "nodeType": "956", "messageId": "957", "endLine": 19, "endColumn": 23}, {"ruleId": "954", "severity": 1, "message": "987", "line": 20, "column": 14, "nodeType": "956", "messageId": "957", "endLine": 20, "endColumn": 25}, {"ruleId": "954", "severity": 1, "message": "988", "line": 25, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 25, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "989", "line": 28, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 28, "endColumn": 26}, {"ruleId": "954", "severity": 1, "message": "990", "line": 48, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 48, "endColumn": 22}, {"ruleId": "954", "severity": 1, "message": "991", "line": 53, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 53, "endColumn": 20}, {"ruleId": "954", "severity": 1, "message": "992", "line": 11, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 11, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "993", "line": 4, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 4, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "994", "line": 5, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "995", "line": 7, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 7, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "996", "line": 12, "column": 14, "nodeType": "956", "messageId": "957", "endLine": 12, "endColumn": 25}, {"ruleId": "954", "severity": 1, "message": "982", "line": 13, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 13, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "997", "line": 17, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 17, "endColumn": 23}, {"ruleId": "954", "severity": 1, "message": "989", "line": 21, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 21, "endColumn": 26}, {"ruleId": "954", "severity": 1, "message": "998", "line": 26, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 26, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "976", "line": 8, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 8, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "977", "line": 9, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 9, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "995", "line": 11, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 11, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "999", "line": 14, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 14, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "1000", "line": 20, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 20, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1001", "line": 21, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 21, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1002", "line": 22, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 22, "endColumn": 9}, {"ruleId": "954", "severity": 1, "message": "1003", "line": 23, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 23, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "1004", "line": 26, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 26, "endColumn": 16}, {"ruleId": "954", "severity": 1, "message": "1005", "line": 30, "column": 15, "nodeType": "956", "messageId": "957", "endLine": 30, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "1006", "line": 32, "column": 14, "nodeType": "956", "messageId": "957", "endLine": 32, "endColumn": 25}, {"ruleId": "954", "severity": 1, "message": "1007", "line": 33, "column": 15, "nodeType": "956", "messageId": "957", "endLine": 33, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "1008", "line": 34, "column": 15, "nodeType": "956", "messageId": "957", "endLine": 34, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "1009", "line": 35, "column": 27, "nodeType": "956", "messageId": "957", "endLine": 35, "endColumn": 51}, {"ruleId": "954", "severity": 1, "message": "1010", "line": 42, "column": 15, "nodeType": "956", "messageId": "957", "endLine": 42, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "1011", "line": 51, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 51, "endColumn": 24}, {"ruleId": "954", "severity": 1, "message": "1012", "line": 52, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 52, "endColumn": 16}, {"ruleId": "954", "severity": 1, "message": "989", "line": 65, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 65, "endColumn": 26}, {"ruleId": "954", "severity": 1, "message": "1013", "line": 66, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 66, "endColumn": 32}, {"ruleId": "954", "severity": 1, "message": "967", "line": 66, "column": 34, "nodeType": "956", "messageId": "957", "endLine": 66, "endColumn": 58}, {"ruleId": "954", "severity": 1, "message": "1014", "line": 66, "column": 60, "nodeType": "956", "messageId": "957", "endLine": 66, "endColumn": 82}, {"ruleId": "954", "severity": 1, "message": "968", "line": 66, "column": 84, "nodeType": "956", "messageId": "957", "endLine": 66, "endColumn": 109}, {"ruleId": "954", "severity": 1, "message": "1015", "line": 66, "column": 111, "nodeType": "956", "messageId": "957", "endLine": 66, "endColumn": 133}, {"ruleId": "954", "severity": 1, "message": "1016", "line": 66, "column": 135, "nodeType": "956", "messageId": "957", "endLine": 66, "endColumn": 160}, {"ruleId": "954", "severity": 1, "message": "1017", "line": 67, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 67, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "998", "line": 69, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 69, "endColumn": 22}, {"ruleId": "954", "severity": 1, "message": "1018", "line": 289, "column": 19, "nodeType": "956", "messageId": "957", "endLine": 289, "endColumn": 29}, {"ruleId": "954", "severity": 1, "message": "1019", "line": 297, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 297, "endColumn": 28}, {"ruleId": "954", "severity": 1, "message": "1020", "line": 298, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 298, "endColumn": 23}, {"ruleId": "954", "severity": 1, "message": "1021", "line": 298, "column": 25, "nodeType": "956", "messageId": "957", "endLine": 298, "endColumn": 41}, {"ruleId": "1022", "severity": 1, "message": "1023", "line": 660, "column": 6, "nodeType": "1024", "endLine": 660, "endColumn": 15, "suggestions": "1025"}, {"ruleId": "954", "severity": 1, "message": "1026", "line": 858, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 858, "endColumn": 33}, {"ruleId": "954", "severity": 1, "message": "1027", "line": 1, "column": 27, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 36}, {"ruleId": "954", "severity": 1, "message": "1028", "line": 49, "column": 19, "nodeType": "956", "messageId": "957", "endLine": 49, "endColumn": 26}, {"ruleId": "1022", "severity": 1, "message": "1029", "line": 178, "column": 6, "nodeType": "1024", "endLine": 178, "endColumn": 38, "suggestions": "1030"}, {"ruleId": "954", "severity": 1, "message": "993", "line": 4, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 4, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "994", "line": 5, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "998", "line": 26, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 26, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "1031", "line": 48, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 48, "endColumn": 29}, {"ruleId": "954", "severity": 1, "message": "993", "line": 4, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 4, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1031", "line": 37, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 37, "endColumn": 29}, {"ruleId": "954", "severity": 1, "message": "993", "line": 4, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 4, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1031", "line": 52, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 52, "endColumn": 29}, {"ruleId": "954", "severity": 1, "message": "993", "line": 4, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 4, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "994", "line": 5, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "998", "line": 26, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 26, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "1031", "line": 48, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 48, "endColumn": 29}, {"ruleId": "954", "severity": 1, "message": "989", "line": 24, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 24, "endColumn": 26}, {"ruleId": "1022", "severity": 1, "message": "1032", "line": 53, "column": 6, "nodeType": "1024", "endLine": 53, "endColumn": 18, "suggestions": "1033"}, {"ruleId": "954", "severity": 1, "message": "1034", "line": 1, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1035", "line": 5, "column": 7, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "981", "line": 14, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 14, "endColumn": 10}, {"ruleId": "954", "severity": 1, "message": "1036", "line": 28, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 28, "endColumn": 18}, {"ruleId": "954", "severity": 1, "message": "1034", "line": 1, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1035", "line": 5, "column": 7, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "976", "line": 8, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 8, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "977", "line": 9, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 9, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "955", "line": 10, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 10, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1037", "line": 23, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 23, "endColumn": 15}, {"ruleId": "954", "severity": 1, "message": "1038", "line": 24, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 24, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "981", "line": 25, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 25, "endColumn": 10}, {"ruleId": "954", "severity": 1, "message": "995", "line": 29, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 29, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1039", "line": 30, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 30, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "1040", "line": 31, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 31, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1041", "line": 32, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 32, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1042", "line": 33, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 33, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "1043", "line": 34, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 34, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1044", "line": 35, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 35, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "985", "line": 39, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 39, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "983", "line": 43, "column": 15, "nodeType": "956", "messageId": "957", "endLine": 43, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "1045", "line": 44, "column": 14, "nodeType": "956", "messageId": "957", "endLine": 44, "endColumn": 25}, {"ruleId": "954", "severity": 1, "message": "1046", "line": 50, "column": 69, "nodeType": "956", "messageId": "957", "endLine": 50, "endColumn": 76}, {"ruleId": "954", "severity": 1, "message": "1047", "line": 79, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 79, "endColumn": 26}, {"ruleId": "1022", "severity": 1, "message": "1048", "line": 161, "column": 6, "nodeType": "1024", "endLine": 161, "endColumn": 8, "suggestions": "1049"}, {"ruleId": "954", "severity": 1, "message": "1050", "line": 675, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 675, "endColumn": 26}, {"ruleId": "958", "severity": 1, "message": "959", "line": 260, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 264, "endColumn": 11}, {"ruleId": "958", "severity": 1, "message": "959", "line": 274, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 274, "endColumn": 70}, {"ruleId": "958", "severity": 1, "message": "959", "line": 278, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 278, "endColumn": 54}, {"ruleId": "958", "severity": 1, "message": "959", "line": 333, "column": 11, "nodeType": "960", "messageId": "961", "endLine": 338, "endColumn": 13}, {"ruleId": "958", "severity": 1, "message": "959", "line": 435, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 439, "endColumn": 11}, {"ruleId": "958", "severity": 1, "message": "959", "line": 451, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 451, "endColumn": 54}, {"ruleId": "958", "severity": 1, "message": "959", "line": 653, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 653, "endColumn": 163}, {"ruleId": "958", "severity": 1, "message": "959", "line": 662, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 662, "endColumn": 70}, {"ruleId": "958", "severity": 1, "message": "959", "line": 666, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 666, "endColumn": 54}, {"ruleId": "954", "severity": 1, "message": "1051", "line": 740, "column": 17, "nodeType": "956", "messageId": "957", "endLine": 740, "endColumn": 22}, {"ruleId": "958", "severity": 1, "message": "959", "line": 760, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 764, "endColumn": 11}, {"ruleId": "958", "severity": 1, "message": "959", "line": 779, "column": 11, "nodeType": "960", "messageId": "961", "endLine": 783, "endColumn": 13}, {"ruleId": "958", "severity": 1, "message": "959", "line": 786, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 789, "endColumn": 11}, {"ruleId": "958", "severity": 1, "message": "959", "line": 795, "column": 11, "nodeType": "960", "messageId": "961", "endLine": 799, "endColumn": 13}, {"ruleId": "958", "severity": 1, "message": "959", "line": 802, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 805, "endColumn": 11}, {"ruleId": "958", "severity": 1, "message": "959", "line": 870, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 874, "endColumn": 11}, {"ruleId": "1052", "severity": 1, "message": "1053", "line": 940, "column": 3, "nodeType": "1054", "messageId": "1055", "endLine": 940, "endColumn": 29}, {"ruleId": "958", "severity": 1, "message": "959", "line": 1237, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 1237, "endColumn": 163}, {"ruleId": "958", "severity": 1, "message": "959", "line": 1267, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 1267, "endColumn": 163}, {"ruleId": "958", "severity": 1, "message": "959", "line": 1320, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 1320, "endColumn": 163}, {"ruleId": "958", "severity": 1, "message": "959", "line": 1367, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 1367, "endColumn": 163}, {"ruleId": "954", "severity": 1, "message": "1056", "line": 6, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 6, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "981", "line": 11, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 11, "endColumn": 10}, {"ruleId": "954", "severity": 1, "message": "1057", "line": 20, "column": 13, "nodeType": "956", "messageId": "957", "endLine": 20, "endColumn": 23}, {"ruleId": "954", "severity": 1, "message": "1058", "line": 205, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 205, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "993", "line": 2, "column": 15, "nodeType": "956", "messageId": "957", "endLine": 2, "endColumn": 25}, {"ruleId": "954", "severity": 1, "message": "1059", "line": 2, "column": 64, "nodeType": "956", "messageId": "957", "endLine": 2, "endColumn": 70}, {"ruleId": "954", "severity": 1, "message": "1008", "line": 4, "column": 15, "nodeType": "956", "messageId": "957", "endLine": 4, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "1060", "line": 5, "column": 12, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "1061", "line": 6, "column": 17, "nodeType": "956", "messageId": "957", "endLine": 6, "endColumn": 26}, {"ruleId": "954", "severity": 1, "message": "1010", "line": 7, "column": 15, "nodeType": "956", "messageId": "957", "endLine": 7, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "1062", "line": 8, "column": 16, "nodeType": "956", "messageId": "957", "endLine": 8, "endColumn": 25}, {"ruleId": "954", "severity": 1, "message": "1063", "line": 14, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 14, "endColumn": 20}, {"ruleId": "954", "severity": 1, "message": "1064", "line": 121, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 121, "endColumn": 29}, {"ruleId": "954", "severity": 1, "message": "1034", "line": 1, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1035", "line": 5, "column": 7, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1065", "line": 3, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 3, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "1066", "line": 4, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 4, "endColumn": 6}, {"ruleId": "954", "severity": 1, "message": "1067", "line": 5, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "1068", "line": 6, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 6, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "1069", "line": 7, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 7, "endColumn": 6}, {"ruleId": "954", "severity": 1, "message": "1070", "line": 12, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 12, "endColumn": 9}, {"ruleId": "954", "severity": 1, "message": "1071", "line": 36, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 36, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "1072", "line": 50, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 50, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "1073", "line": 64, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 64, "endColumn": 20}, {"ruleId": "954", "severity": 1, "message": "1074", "line": 88, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 88, "endColumn": 22}, {"ruleId": "954", "severity": 1, "message": "1075", "line": 104, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 104, "endColumn": 30}, {"ruleId": "954", "severity": 1, "message": "1076", "line": 3, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 3, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1068", "line": 3, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 3, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "1069", "line": 4, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 4, "endColumn": 6}, {"ruleId": "954", "severity": 1, "message": "1077", "line": 5, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "1078", "line": 6, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 6, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "1079", "line": 7, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 7, "endColumn": 16}, {"ruleId": "954", "severity": 1, "message": "1080", "line": 8, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 8, "endColumn": 10}, {"ruleId": "954", "severity": 1, "message": "1070", "line": 9, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 9, "endColumn": 9}, {"ruleId": "954", "severity": 1, "message": "1081", "line": 10, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 10, "endColumn": 22}, {"ruleId": "954", "severity": 1, "message": "1065", "line": 11, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 11, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "1066", "line": 12, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 12, "endColumn": 6}, {"ruleId": "954", "severity": 1, "message": "1067", "line": 13, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 13, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "1082", "line": 14, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 14, "endColumn": 16}, {"ruleId": "954", "severity": 1, "message": "1083", "line": 15, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 15, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "1076", "line": 16, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 16, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1084", "line": 18, "column": 40, "nodeType": "956", "messageId": "957", "endLine": 18, "endColumn": 44}, {"ruleId": "954", "severity": 1, "message": "1085", "line": 47, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 47, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "1086", "line": 64, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 64, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "1087", "line": 71, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 71, "endColumn": 20}, {"ruleId": "954", "severity": 1, "message": "1074", "line": 79, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 79, "endColumn": 22}, {"ruleId": "954", "severity": 1, "message": "1075", "line": 95, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 95, "endColumn": 30}, {"ruleId": "954", "severity": 1, "message": "1088", "line": 272, "column": 27, "nodeType": "956", "messageId": "957", "endLine": 272, "endColumn": 37}, {"ruleId": "954", "severity": 1, "message": "1089", "line": 273, "column": 27, "nodeType": "956", "messageId": "957", "endLine": 273, "endColumn": 36}, {"ruleId": "954", "severity": 1, "message": "994", "line": 3, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 3, "endColumn": 8}, {"ruleId": "1022", "severity": 1, "message": "1090", "line": 60, "column": 6, "nodeType": "1024", "endLine": 60, "endColumn": 34, "suggestions": "1091"}, {"ruleId": "954", "severity": 1, "message": "1092", "line": 25, "column": 13, "nodeType": "956", "messageId": "957", "endLine": 25, "endColumn": 25}, {"ruleId": "954", "severity": 1, "message": "1093", "line": 33, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 33, "endColumn": 15}, {"ruleId": "954", "severity": 1, "message": "1094", "line": 34, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 34, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1095", "line": 35, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 35, "endColumn": 22}, {"ruleId": "954", "severity": 1, "message": "1096", "line": 36, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 36, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "1097", "line": 37, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 37, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "1098", "line": 41, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 41, "endColumn": 20}, {"ruleId": "954", "severity": 1, "message": "1099", "line": 43, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 43, "endColumn": 34}, {"ruleId": "954", "severity": 1, "message": "1100", "line": 69, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 69, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "1101", "line": 69, "column": 19, "nodeType": "956", "messageId": "957", "endLine": 69, "endColumn": 29}, {"ruleId": "1022", "severity": 1, "message": "1102", "line": 88, "column": 6, "nodeType": "1024", "endLine": 88, "endColumn": 18, "suggestions": "1103"}, {"ruleId": "1022", "severity": 1, "message": "1104", "line": 448, "column": 6, "nodeType": "1024", "endLine": 448, "endColumn": 28, "suggestions": "1105"}, {"ruleId": "954", "severity": 1, "message": "980", "line": 4, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 4, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1057", "line": 21, "column": 20, "nodeType": "956", "messageId": "957", "endLine": 21, "endColumn": 30}, {"ruleId": "954", "severity": 1, "message": "1058", "line": 100, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 100, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "1106", "line": 119, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 119, "endColumn": 30}, {"ruleId": "954", "severity": 1, "message": "1107", "line": 8, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 8, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "1108", "line": 9, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 9, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "1109", "line": 10, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 10, "endColumn": 15}, {"ruleId": "954", "severity": 1, "message": "1110", "line": 12, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 12, "endColumn": 9}, {"ruleId": "954", "severity": 1, "message": "1111", "line": 13, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 13, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1112", "line": 14, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 14, "endColumn": 16}, {"ruleId": "954", "severity": 1, "message": "1113", "line": 15, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 15, "endColumn": 16}, {"ruleId": "954", "severity": 1, "message": "1114", "line": 36, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 36, "endColumn": 30}, {"ruleId": "954", "severity": 1, "message": "1115", "line": 37, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 37, "endColumn": 20}, {"ruleId": "1022", "severity": 1, "message": "1116", "line": 46, "column": 6, "nodeType": "1024", "endLine": 46, "endColumn": 18, "suggestions": "1117"}, {"ruleId": "954", "severity": 1, "message": "1118", "line": 265, "column": 23, "nodeType": "956", "messageId": "957", "endLine": 265, "endColumn": 44}, {"ruleId": "954", "severity": 1, "message": "1119", "line": 266, "column": 23, "nodeType": "956", "messageId": "957", "endLine": 266, "endColumn": 42}, {"ruleId": "954", "severity": 1, "message": "1118", "line": 381, "column": 21, "nodeType": "956", "messageId": "957", "endLine": 381, "endColumn": 42}, {"ruleId": "954", "severity": 1, "message": "1119", "line": 382, "column": 21, "nodeType": "956", "messageId": "957", "endLine": 382, "endColumn": 40}, {"ruleId": "954", "severity": 1, "message": "1034", "line": 1, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1035", "line": 5, "column": 7, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1120", "line": 1, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1034", "line": 1, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1035", "line": 5, "column": 7, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 14}, {"ruleId": "1052", "severity": 1, "message": "1121", "line": 127, "column": 3, "nodeType": "1054", "messageId": "1055", "endLine": 127, "endColumn": 19}, {"ruleId": "1052", "severity": 1, "message": "1122", "line": 138, "column": 3, "nodeType": "1054", "messageId": "1055", "endLine": 138, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "1123", "line": 267, "column": 13, "nodeType": "956", "messageId": "957", "endLine": 267, "endColumn": 20}, {"ruleId": "1052", "severity": 1, "message": "1124", "line": 292, "column": 3, "nodeType": "1054", "messageId": "1055", "endLine": 292, "endColumn": 29}, {"ruleId": "954", "severity": 1, "message": "1034", "line": 1, "column": 8, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1035", "line": 5, "column": 7, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1125", "line": 83, "column": 13, "nodeType": "956", "messageId": "957", "endLine": 83, "endColumn": 21}, {"ruleId": "958", "severity": 1, "message": "959", "line": 109, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 109, "endColumn": 163}, {"ruleId": "958", "severity": 1, "message": "959", "line": 123, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 123, "endColumn": 70}, {"ruleId": "958", "severity": 1, "message": "959", "line": 127, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 127, "endColumn": 54}, {"ruleId": "958", "severity": 1, "message": "959", "line": 212, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 212, "endColumn": 163}, {"ruleId": "958", "severity": 1, "message": "959", "line": 226, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 226, "endColumn": 70}, {"ruleId": "958", "severity": 1, "message": "959", "line": 230, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 230, "endColumn": 54}, {"ruleId": "958", "severity": 1, "message": "959", "line": 271, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 271, "endColumn": 163}, {"ruleId": "958", "severity": 1, "message": "959", "line": 280, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 280, "endColumn": 70}, {"ruleId": "958", "severity": 1, "message": "959", "line": 284, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 284, "endColumn": 54}, {"ruleId": "958", "severity": 1, "message": "959", "line": 320, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 320, "endColumn": 70}, {"ruleId": "958", "severity": 1, "message": "959", "line": 324, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 324, "endColumn": 54}, {"ruleId": "958", "severity": 1, "message": "959", "line": 416, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 416, "endColumn": 163}, {"ruleId": "958", "severity": 1, "message": "959", "line": 425, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 425, "endColumn": 70}, {"ruleId": "958", "severity": 1, "message": "959", "line": 429, "column": 9, "nodeType": "960", "messageId": "961", "endLine": 429, "endColumn": 54}, {"ruleId": "954", "severity": 1, "message": "1100", "line": 60, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 60, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "1101", "line": 60, "column": 19, "nodeType": "956", "messageId": "957", "endLine": 60, "endColumn": 29}, {"ruleId": "1022", "severity": 1, "message": "1116", "line": 90, "column": 6, "nodeType": "1024", "endLine": 90, "endColumn": 32, "suggestions": "1126"}, {"ruleId": "954", "severity": 1, "message": "1127", "line": 370, "column": 17, "nodeType": "956", "messageId": "957", "endLine": 370, "endColumn": 23}, {"ruleId": "954", "severity": 1, "message": "1128", "line": 470, "column": 17, "nodeType": "956", "messageId": "957", "endLine": 470, "endColumn": 25}, {"ruleId": "954", "severity": 1, "message": "1129", "line": 17, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 17, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "1003", "line": 16, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 16, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "1002", "line": 17, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 17, "endColumn": 9}, {"ruleId": "954", "severity": 1, "message": "1001", "line": 19, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 19, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1004", "line": 14, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 14, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "1130", "line": 43, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 43, "endColumn": 26}, {"ruleId": "954", "severity": 1, "message": "995", "line": 12, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 12, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1131", "line": 25, "column": 17, "nodeType": "956", "messageId": "957", "endLine": 25, "endColumn": 29}, {"ruleId": "954", "severity": 1, "message": "1132", "line": 33, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 33, "endColumn": 29}, {"ruleId": "954", "severity": 1, "message": "1133", "line": 3, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 3, "endColumn": 6}, {"ruleId": "954", "severity": 1, "message": "981", "line": 9, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 9, "endColumn": 10}, {"ruleId": "954", "severity": 1, "message": "1092", "line": 58, "column": 15, "nodeType": "956", "messageId": "957", "endLine": 58, "endColumn": 27}, {"ruleId": "1022", "severity": 1, "message": "1134", "line": 140, "column": 6, "nodeType": "1024", "endLine": 140, "endColumn": 18, "suggestions": "1135"}, {"ruleId": "1022", "severity": 1, "message": "1136", "line": 145, "column": 6, "nodeType": "1024", "endLine": 145, "endColumn": 52, "suggestions": "1137"}, {"ruleId": "1022", "severity": 1, "message": "1138", "line": 150, "column": 6, "nodeType": "1024", "endLine": 150, "endColumn": 62, "suggestions": "1139"}, {"ruleId": "1022", "severity": 1, "message": "1140", "line": 155, "column": 6, "nodeType": "1024", "endLine": 155, "endColumn": 28, "suggestions": "1141"}, {"ruleId": "1022", "severity": 1, "message": "1142", "line": 164, "column": 6, "nodeType": "1024", "endLine": 164, "endColumn": 39, "suggestions": "1143"}, {"ruleId": "954", "severity": 1, "message": "1144", "line": 39, "column": 13, "nodeType": "956", "messageId": "957", "endLine": 39, "endColumn": 23}, {"ruleId": "1022", "severity": 1, "message": "1145", "line": 71, "column": 6, "nodeType": "1024", "endLine": 71, "endColumn": 18, "suggestions": "1146"}, {"ruleId": "954", "severity": 1, "message": "1027", "line": 1, "column": 27, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 36}, {"ruleId": "954", "severity": 1, "message": "976", "line": 10, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 10, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "977", "line": 11, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 11, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "993", "line": 12, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 12, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1129", "line": 27, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 27, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "984", "line": 30, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 30, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "1147", "line": 33, "column": 17, "nodeType": "956", "messageId": "957", "endLine": 33, "endColumn": 25}, {"ruleId": "954", "severity": 1, "message": "966", "line": 34, "column": 17, "nodeType": "956", "messageId": "957", "endLine": 34, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "996", "line": 35, "column": 14, "nodeType": "956", "messageId": "957", "endLine": 35, "endColumn": 25}, {"ruleId": "954", "severity": 1, "message": "976", "line": 10, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 10, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "977", "line": 11, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 11, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1129", "line": 27, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 27, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "1148", "line": 28, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 28, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1149", "line": 29, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 29, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "1150", "line": 30, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 30, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "984", "line": 34, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 34, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "1151", "line": 37, "column": 17, "nodeType": "956", "messageId": "957", "endLine": 37, "endColumn": 31}, {"ruleId": "1022", "severity": 1, "message": "1152", "line": 98, "column": 6, "nodeType": "1024", "endLine": 98, "endColumn": 24, "suggestions": "1153"}, {"ruleId": "954", "severity": 1, "message": "976", "line": 4, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 4, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "977", "line": 5, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1154", "line": 29, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 29, "endColumn": 15}, {"ruleId": "1022", "severity": 1, "message": "1155", "line": 80, "column": 6, "nodeType": "1024", "endLine": 80, "endColumn": 25, "suggestions": "1156"}, {"ruleId": "1022", "severity": 1, "message": "1157", "line": 87, "column": 6, "nodeType": "1024", "endLine": 87, "endColumn": 24, "suggestions": "1158"}, {"ruleId": "954", "severity": 1, "message": "1159", "line": 145, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 145, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "1160", "line": 196, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 196, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "1161", "line": 233, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 233, "endColumn": 24}, {"ruleId": "1022", "severity": 1, "message": "1162", "line": 389, "column": 6, "nodeType": "1024", "endLine": 389, "endColumn": 58, "suggestions": "1163"}, {"ruleId": "954", "severity": 1, "message": "1000", "line": 15, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 15, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1001", "line": 16, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 16, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1002", "line": 17, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 17, "endColumn": 9}, {"ruleId": "954", "severity": 1, "message": "1003", "line": 18, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 18, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "1109", "line": 21, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 21, "endColumn": 15}, {"ruleId": "954", "severity": 1, "message": "1164", "line": 28, "column": 12, "nodeType": "956", "messageId": "957", "endLine": 28, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "1098", "line": 33, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 33, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "1165", "line": 78, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 78, "endColumn": 24}, {"ruleId": "954", "severity": 1, "message": "1039", "line": 8, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 8, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "1040", "line": 9, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 9, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1041", "line": 10, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 10, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1042", "line": 11, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 11, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "1043", "line": 12, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 12, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1044", "line": 13, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 13, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "1129", "line": 15, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 15, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "981", "line": 25, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 25, "endColumn": 10}, {"ruleId": "954", "severity": 1, "message": "1109", "line": 30, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 30, "endColumn": 15}, {"ruleId": "954", "severity": 1, "message": "1080", "line": 32, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 32, "endColumn": 10}, {"ruleId": "954", "severity": 1, "message": "1056", "line": 33, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 33, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "1057", "line": 40, "column": 13, "nodeType": "956", "messageId": "957", "endLine": 40, "endColumn": 23}, {"ruleId": "954", "severity": 1, "message": "1010", "line": 42, "column": 15, "nodeType": "956", "messageId": "957", "endLine": 42, "endColumn": 27}, {"ruleId": "954", "severity": 1, "message": "1093", "line": 50, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 50, "endColumn": 15}, {"ruleId": "954", "severity": 1, "message": "1094", "line": 51, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 51, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1095", "line": 52, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 52, "endColumn": 22}, {"ruleId": "954", "severity": 1, "message": "1096", "line": 53, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 53, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "1097", "line": 54, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 54, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "1166", "line": 55, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 55, "endColumn": 15}, {"ruleId": "954", "severity": 1, "message": "1167", "line": 56, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 56, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "1168", "line": 57, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 57, "endColumn": 21}, {"ruleId": "954", "severity": 1, "message": "1098", "line": 58, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 58, "endColumn": 20}, {"ruleId": "1022", "severity": 1, "message": "1169", "line": 96, "column": 6, "nodeType": "1024", "endLine": 96, "endColumn": 32, "suggestions": "1170"}, {"ruleId": "954", "severity": 1, "message": "1171", "line": 223, "column": 13, "nodeType": "956", "messageId": "957", "endLine": 223, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "1131", "line": 21, "column": 17, "nodeType": "956", "messageId": "957", "endLine": 21, "endColumn": 29}, {"ruleId": "954", "severity": 1, "message": "1027", "line": 1, "column": 27, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 36}, {"ruleId": "954", "severity": 1, "message": "1172", "line": 51, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 51, "endColumn": 30}, {"ruleId": "954", "severity": 1, "message": "1173", "line": 52, "column": 29, "nodeType": "956", "messageId": "957", "endLine": 52, "endColumn": 49}, {"ruleId": "954", "severity": 1, "message": "1174", "line": 242, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 242, "endColumn": 36}, {"ruleId": "954", "severity": 1, "message": "1175", "line": 246, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 246, "endColumn": 38}, {"ruleId": "954", "severity": 1, "message": "1176", "line": 75, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 75, "endColumn": 25}, {"ruleId": "954", "severity": 1, "message": "1177", "line": 123, "column": 11, "nodeType": "956", "messageId": "957", "endLine": 123, "endColumn": 22}, {"ruleId": "1178", "severity": 1, "message": "1179", "line": 126, "column": 5, "nodeType": "1180", "messageId": "1181", "endLine": 201, "endColumn": 6}, {"ruleId": "1178", "severity": 1, "message": "1179", "line": 219, "column": 5, "nodeType": "1180", "messageId": "1181", "endLine": 279, "endColumn": 6}, {"ruleId": "1178", "severity": 1, "message": "1179", "line": 290, "column": 5, "nodeType": "1180", "messageId": "1181", "endLine": 336, "endColumn": 6}, {"ruleId": "1182", "severity": 1, "message": "1183", "line": 429, "column": 1, "nodeType": "1184", "endLine": 429, "endColumn": 47}, {"ruleId": "1185", "severity": 1, "message": "1186", "line": 146, "column": 25, "nodeType": "1187", "messageId": "1188", "endLine": 146, "endColumn": 26, "suggestions": "1189"}, {"ruleId": "1185", "severity": 1, "message": "1190", "line": 146, "column": 37, "nodeType": "1187", "messageId": "1188", "endLine": 146, "endColumn": 38, "suggestions": "1191"}, {"ruleId": "1185", "severity": 1, "message": "1192", "line": 146, "column": 39, "nodeType": "1187", "messageId": "1188", "endLine": 146, "endColumn": 40, "suggestions": "1193"}, {"ruleId": "954", "severity": 1, "message": "976", "line": 4, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 4, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "977", "line": 5, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 5, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1039", "line": 8, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 8, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "1040", "line": 9, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 9, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1041", "line": 10, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 10, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1042", "line": 11, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 11, "endColumn": 17}, {"ruleId": "954", "severity": 1, "message": "1043", "line": 12, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 12, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1044", "line": 13, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 13, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "994", "line": 14, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 14, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "981", "line": 31, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 31, "endColumn": 10}, {"ruleId": "1022", "severity": 1, "message": "1194", "line": 64, "column": 6, "nodeType": "1024", "endLine": 64, "endColumn": 24, "suggestions": "1195"}, {"ruleId": "954", "severity": 1, "message": "1084", "line": 8, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 8, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "1107", "line": 16, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 16, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "1108", "line": 17, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 17, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "1109", "line": 18, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 18, "endColumn": 15}, {"ruleId": "954", "severity": 1, "message": "1003", "line": 20, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 20, "endColumn": 11}, {"ruleId": "954", "severity": 1, "message": "981", "line": 21, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 21, "endColumn": 10}, {"ruleId": "954", "severity": 1, "message": "964", "line": 30, "column": 19, "nodeType": "956", "messageId": "957", "endLine": 30, "endColumn": 35}, {"ruleId": "954", "severity": 1, "message": "1196", "line": 434, "column": 9, "nodeType": "956", "messageId": "957", "endLine": 434, "endColumn": 22}, {"ruleId": "1022", "severity": 1, "message": "1197", "line": 164, "column": 6, "nodeType": "1024", "endLine": 164, "endColumn": 18, "suggestions": "1198", "suppressions": "1199"}, {"ruleId": "1022", "severity": 1, "message": "1200", "line": 238, "column": 6, "nodeType": "1024", "endLine": 238, "endColumn": 88, "suggestions": "1201", "suppressions": "1202"}, {"ruleId": "954", "severity": 1, "message": "1203", "line": 1, "column": 17, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 25}, {"ruleId": "954", "severity": 1, "message": "1027", "line": 1, "column": 27, "nodeType": "956", "messageId": "957", "endLine": 1, "endColumn": 36}, {"ruleId": "954", "severity": 1, "message": "976", "line": 29, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 29, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "977", "line": 30, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 30, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "955", "line": 31, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 31, "endColumn": 14}, {"ruleId": "954", "severity": 1, "message": "1148", "line": 32, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 32, "endColumn": 12}, {"ruleId": "954", "severity": 1, "message": "1149", "line": 33, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 33, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "1150", "line": 34, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 34, "endColumn": 19}, {"ruleId": "954", "severity": 1, "message": "1151", "line": 42, "column": 17, "nodeType": "956", "messageId": "957", "endLine": 42, "endColumn": 31}, {"ruleId": "954", "severity": 1, "message": "1204", "line": 100, "column": 10, "nodeType": "956", "messageId": "957", "endLine": 100, "endColumn": 24}, {"ruleId": "954", "severity": 1, "message": "1205", "line": 101, "column": 25, "nodeType": "956", "messageId": "957", "endLine": 101, "endColumn": 41}, {"ruleId": "1022", "severity": 1, "message": "1206", "line": 141, "column": 6, "nodeType": "1024", "endLine": 141, "endColumn": 16, "suggestions": "1207"}, {"ruleId": "1178", "severity": 1, "message": "1179", "line": 146, "column": 7, "nodeType": "1180", "messageId": "1181", "endLine": 165, "endColumn": 8}, {"ruleId": "954", "severity": 1, "message": "975", "line": 9, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 9, "endColumn": 7}, {"ruleId": "954", "severity": 1, "message": "995", "line": 21, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 21, "endColumn": 13}, {"ruleId": "954", "severity": 1, "message": "1080", "line": 22, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 22, "endColumn": 10}, {"ruleId": "1022", "severity": 1, "message": "1208", "line": 49, "column": 6, "nodeType": "1024", "endLine": 49, "endColumn": 21, "suggestions": "1209"}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Avatar' is defined but never used.", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'setOpenEliminaCavoDialog' is assigned a value but never used.", "'setOpenModificaCavoDialog' is assigned a value but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'TextField' is defined but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'Typography' is defined but never used.", "'Paper' is defined but never used.", "'IconButton' is defined but never used.", "'RefreshIcon' is defined but never used.", "'AdminHomeButton' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'LinearProgress' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'InfoIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'LinkOffIcon' is defined but never used.", "'TimelineIcon' is defined but never used.", "'CheckBoxIcon' is defined but never used.", "'CheckBoxOutlineBlankIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'parcoCaviService' is defined but never used.", "'CavoForm' is defined but never used.", "'openEliminaCavoDialog' is assigned a value but never used.", "'openModificaCavoDialog' is assigned a value but never used.", "'openAggiungiCavoDialog' is assigned a value but never used.", "'setOpenAggiungiCavoDialog' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'calculateStatistics', 'caviAttivi', 'caviSpare', 'error', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["1210"], "'handleCreateCommandError' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStoricoBobine'. Either include it or remove the dependency array.", ["1211"], "'handleBackToCantieri' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["1212"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["1213"], "'renderBobineCards' is assigned a value but never used.", "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "'Stack' is defined but never used.", "'CancelIcon' is defined but never used.", "'handleCancel' is assigned a value but never used.", "'Button' is defined but never used.", "'ClearIcon' is defined but never used.", "'RulerIcon' is defined but never used.", "'StartIcon' is defined but never used.", "'formatDate' is defined but never used.", "'handleClearSelection' is assigned a value but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadCavi' and 'loadWeatherData'. Either include them or remove the dependency array.", ["1214"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["1215"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["1216"], "'handleBackToSelection' is assigned a value but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'internalSelectedCavo' is assigned a value but never used.", "'openDialog' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1217"], "'latoPartenzaCollegato' is assigned a value but never used.", "'latoArrivoCollegato' is assigned a value but never used.", "'config' is defined but never used.", "Duplicate key 'aggiornaDatiPosa'.", "Duplicate key 'aggiornaDatiCollegamento'.", "'payload' is assigned a value but never used.", "Duplicate key 'aggiornaDatiCertificazione'.", "'sentData' is assigned a value but never used.", ["1218"], "'result' is assigned a value but never used.", "'hasMetri' is assigned a value but never used.", "'Alert' is defined but never used.", "'filteredCantieri' is assigned a value but never used.", "'LocationIcon' is defined but never used.", "'currentHoldDuration' is assigned a value but never used.", "'Box' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1219"], "React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array.", ["1220"], "React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array.", ["1221"], "React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array.", ["1222"], "React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array.", ["1223"], "'PeopleIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array.", ["1224"], "'ViewIcon' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProve'. Either include it or remove the dependency array.", ["1225"], "'Autocomplete' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCaviDisponibili'. Either include it or remove the dependency array.", ["1226"], "React Hook useEffect has a missing dependency: 'loadResponsabiliDisponibili'. Either include it or remove the dependency array.", ["1227"], "'handleBack' is assigned a value but never used.", "'matchesNumericTerm' is assigned a value but never used.", "'isNumericTerm' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'cavoMatchesTerm'. Either include it or remove the dependency array.", ["1228"], "'CloseIcon' is defined but never used.", "'getBobinaNumber' is assigned a value but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["1229"], "'bobina' is assigned a value but never used.", "'showValidationDialog' is assigned a value but never used.", "'setValidationLoading' is assigned a value but never used.", "'handleValidationDialogClose' is assigned a value but never used.", "'handleValidationDialogProceed' is assigned a value but never used.", "'getSeverityColor' is assigned a value but never used.", "'isConnected' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["1230", "1231"], "Unnecessary escape character: \\(.", ["1232", "1233"], "Unnecessary escape character: \\).", ["1234", "1235"], "React Hook useEffect has a missing dependency: 'loadResponsabili'. Either include it or remove the dependency array.", ["1236"], "'getStatoColor' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadComande', 'loadResponsabili', and 'loadStatistiche'. Either include them or remove the dependency array.", ["1237"], ["1238"], "React Hook useEffect has missing dependencies: 'loadResponsabili', 'searchingComanda', and 'setSearchParams'. Either include them or remove the dependency array.", ["1239"], ["1240"], "'useState' is defined but never used.", "'tipologieTotal' is assigned a value but never used.", "'setTipologiePage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["1241"], "React Hook useEffect has a missing dependency: 'loadCaviComanda'. Either include it or remove the dependency array.", ["1242"], {"desc": "1243", "fix": "1244"}, {"desc": "1245", "fix": "1246"}, {"desc": "1247", "fix": "1248"}, {"desc": "1249", "fix": "1250"}, {"desc": "1251", "fix": "1252"}, {"desc": "1253", "fix": "1254"}, {"desc": "1255", "fix": "1256"}, {"desc": "1257", "fix": "1258"}, {"desc": "1259", "fix": "1260"}, {"desc": "1261", "fix": "1262"}, {"desc": "1263", "fix": "1264"}, {"desc": "1265", "fix": "1266"}, {"desc": "1267", "fix": "1268"}, {"desc": "1269", "fix": "1270"}, {"desc": "1271", "fix": "1272"}, {"desc": "1273", "fix": "1274"}, {"desc": "1275", "fix": "1276"}, {"desc": "1277", "fix": "1278"}, {"desc": "1279", "fix": "1280"}, {"desc": "1281", "fix": "1282"}, {"messageId": "1283", "fix": "1284", "desc": "1285"}, {"messageId": "1286", "fix": "1287", "desc": "1288"}, {"messageId": "1283", "fix": "1289", "desc": "1285"}, {"messageId": "1286", "fix": "1290", "desc": "1288"}, {"messageId": "1283", "fix": "1291", "desc": "1285"}, {"messageId": "1286", "fix": "1292", "desc": "1288"}, {"desc": "1293", "fix": "1294"}, {"desc": "1295", "fix": "1296"}, {"kind": "1297", "justification": "1298"}, {"desc": "1299", "fix": "1300"}, {"kind": "1297", "justification": "1298"}, {"desc": "1301", "fix": "1302"}, {"desc": "1303", "fix": "1304"}, "Update the dependencies array to be: [calculateStatistics, caviAttivi, caviSpare, error, filters, user]", {"range": "1305", "text": "1306"}, "Update the dependencies array to be: [cantiereId, loadStoricoBobine, selectedReportType]", {"range": "1307", "text": "1308"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1309", "text": "1310"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1311", "text": "1312"}, "Update the dependencies array to be: [certificazione, cantiereId, loadCavi, loadWeatherData]", {"range": "1313", "text": "1314"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1315", "text": "1316"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1317", "text": "1318"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1319", "text": "1320"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1321", "text": "1322"}, "Update the dependencies array to be: [cantiereId, loadInitialData]", {"range": "1323", "text": "1324"}, "Update the dependencies array to be: [cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", {"range": "1325", "text": "1326"}, "Update the dependencies array to be: [certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", {"range": "1327", "text": "1328"}, "Update the dependencies array to be: [calculateStatistics, cavi, certificazioni]", {"range": "1329", "text": "1330"}, "Update the dependencies array to be: [activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", {"range": "1331", "text": "1332"}, "Update the dependencies array to be: [cantiereId, loadComande, loadStatistiche]", {"range": "1333", "text": "1334"}, "Update the dependencies array to be: [certificazioneId, loadProve]", {"range": "1335", "text": "1336"}, "Update the dependencies array to be: [loadCaviDisponibili, open, tipoComanda]", {"range": "1337", "text": "1338"}, "Update the dependencies array to be: [open, cantiereId, loadResponsabiliDisponibili]", {"range": "1339", "text": "1340"}, "Update the dependencies array to be: [searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", {"range": "1341", "text": "1342"}, "Update the dependencies array to be: [open, cavoPreselezionato, loadBobine]", {"range": "1343", "text": "1344"}, "removeEscape", {"range": "1345", "text": "1298"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1346", "text": "1347"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1348", "text": "1298"}, {"range": "1349", "text": "1347"}, {"range": "1350", "text": "1298"}, {"range": "1351", "text": "1347"}, "Update the dependencies array to be: [open, cantiereId, loadResponsabili]", {"range": "1352", "text": "1353"}, "Update the dependencies array to be: [cantiereId, loadComande, loadResponsabili, loadStatistiche]", {"range": "1354", "text": "1355"}, "directive", "", "Update the dependencies array to be: [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili, searchingComanda, setSearchParams, loadResponsabili]", {"range": "1356", "text": "1357"}, "Update the dependencies array to be: [loadData, tabValue]", {"range": "1358", "text": "1359"}, "Update the dependencies array to be: [open, comanda, loadCaviComanda]", {"range": "1360", "text": "1361"}, [25836, 25845], "[calculateStatistics, caviAttivi, caviSpare, error, filters, user]", [5510, 5542], "[cantiere<PERSON>d, loadStoricoBobine, selectedReportType]", [1559, 1571], "[cantiereId, selectCantiere]", [5793, 5795], "[handleOptionSelect, initialOption, loadBobine]", [1809, 1837], "[certificazione, cantiereId, loadCavi, loadWeatherData]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1077, 1089], "[cantiereId, loadCavi]", [2734, 2760], "[open, bobina, cantiereId, loadCavi]", [3803, 3815], "[cantiereId, loadInitialData]", [3900, 3946], "[cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", [4030, 4086], "[certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", [4192, 4214], "[calculateStatistics, cavi, certificazioni]", [4436, 4469], "[activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", [1672, 1684], "[cantiereId, loadComande, loadStatistiche]", [2516, 2534], "[certificazioneId, loadProve]", [1942, 1961], "[loadCaviDisponibili, open, tipoComanda]", [2125, 2143], "[open, cantiereId, loadResponsabiliDisponibili]", [11274, 11326], "[searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", [2440, 2466], "[open, cavoPreselezionato, loadBobine]", [4732, 4733], [4732, 4732], "\\", [4744, 4745], [4744, 4744], [4746, 4747], [4746, 4746], [1518, 1536], "[open, cantiereId, loadResponsabili]", [5509, 5521], "[cantiereId, loadComande, loadResponsabili, loadStatistiche]", [8413, 8495], "[searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili, searchingComanda, setSearchParams, loadResponsabili]", [3615, 3625], "[loadData, tabValue]", [967, 982], "[open, comanda, loadCaviComanda]"]